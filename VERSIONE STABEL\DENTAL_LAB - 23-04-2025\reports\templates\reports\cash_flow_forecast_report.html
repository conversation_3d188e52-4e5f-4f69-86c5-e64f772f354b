{% extends 'base.html' %}
{% load static %}
{% load humanize %}
{% load common_filters %}

{% block title %}Cash Flow Forecast Report{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
<style>
    .cashflow-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    .cashflow-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .cashflow-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .cashflow-positive {
        color: #28a745;
    }
    .cashflow-negative {
        color: #dc3545;
    }
    .payment-method-item {
        padding: 10px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 5px;
        border-left: 4px solid #007bff;
    }
    .forecast-month {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .seasonal-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }
    .seasonal-high { background-color: #28a745; }
    .seasonal-medium { background-color: #ffc107; }
    .seasonal-low { background-color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-chart-line"></i> Cash Flow Forecast Report
            </h1>

            <!-- Analysis Period Info -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Historical Analysis Period</h6>
                            <p class="mb-0">{{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}</p>
                            <small class="text-muted">12 months of historical data analyzed</small>
                        </div>
                        <div class="col-md-6">
                            <h6>Forecast Period</h6>
                            <p class="mb-0">Next 6 months</p>
                            <small class="text-muted">Based on historical trends and seasonal patterns</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cash Flow KPIs -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="cashflow-card">
                <div class="cashflow-value">
                    {% if monthly_cash_flow %}
                    €{{ monthly_cash_flow|last|get_item:"net_cash_flow"|floatformat:0|intcomma }}
                    {% else %}€0{% endif %}
                </div>
                <div class="cashflow-label">Current Month Net Flow</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="cashflow-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="cashflow-value">
                    {% if monthly_cash_flow %}
                    €{{ monthly_cash_flow|last|get_item:"cumulative_cash_flow"|floatformat:0|intcomma }}
                    {% else %}€0{% endif %}
                </div>
                <div class="cashflow-label">Cumulative Cash Flow</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="cashflow-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="cashflow-value">{{ payment_trends|length }}</div>
                <div class="cashflow-label">Payment Methods</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="cashflow-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="cashflow-value">{{ seasonal_data|length }}</div>
                <div class="cashflow-label">Seasonal Patterns</div>
            </div>
        </div>
    </div>

    <!-- Cash Flow Chart and Forecast -->
    <div class="row">
        <!-- Historical and Forecast Chart -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-area"></i> Cash Flow History & Forecast</h5>
                </div>
                <div class="card-body">
                    <canvas id="cashFlowChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- 6-Month Forecast Details -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-crystal-ball"></i> 6-Month Forecast</h5>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    {% for forecast in forecast_months %}
                    <div class="forecast-month">
                        <h6>{{ forecast.month|date:"M Y" }}</h6>
                        <div class="row">
                            <div class="col-6">
                                <small>Revenue</small><br>
                                <strong>€{{ forecast.revenue|floatformat:0|intcomma }}</strong>
                            </div>
                            <div class="col-6">
                                <small>Payments</small><br>
                                <strong>€{{ forecast.payments|floatformat:0|intcomma }}</strong>
                            </div>
                        </div>
                        <hr class="my-2">
                        <div class="d-flex justify-content-between">
                            <span>Net Cash Flow:</span>
                            <strong class="{% if forecast.net_cash_flow >= 0 %}cashflow-positive{% else %}cashflow-negative{% endif %}">
                                €{{ forecast.net_cash_flow|floatformat:0|intcomma }}
                            </strong>
                        </div>
                    </div>
                    {% empty %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Insufficient historical data for reliable forecasting.
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Trends and Seasonal Analysis -->
    <div class="row mt-4">
        <!-- Payment Method Analysis -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-credit-card"></i> Payment Method Analysis</h5>
                </div>
                <div class="card-body">
                    {% for payment in payment_trends %}
                    <div class="payment-method-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ payment.payment_method|title }}</h6>
                                <small>{{ payment.count }} transactions</small>
                            </div>
                            <div class="text-end">
                                <strong>€{{ payment.total_amount|floatformat:0|intcomma }}</strong><br>
                                <small>Avg: €{{ payment.avg_amount|floatformat:0|intcomma }}</small>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">No payment trend data available.</p>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Seasonal Patterns -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-alt"></i> Seasonal Revenue Patterns</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for season in seasonal_data %}
                        <div class="col-6 mb-3">
                            <div class="d-flex align-items-center">
                                <span class="seasonal-indicator
                                    {% if season.avg_revenue > 15000 %}seasonal-high
                                    {% elif season.avg_revenue > 10000 %}seasonal-medium
                                    {% else %}seasonal-low{% endif %}"></span>
                                <div>
                                    <strong>{{ season.month|date:"F" }}</strong><br>
                                    <small>€{{ season.avg_revenue|floatformat:0|intcomma }}</small>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="col-12">
                            <p class="text-muted">Insufficient data for seasonal analysis.</p>
                        </div>
                        {% endfor %}
                    </div>

                    {% if seasonal_data %}
                    <hr>
                    <div class="small">
                        <span class="seasonal-indicator seasonal-high"></span> High Season (>€15k)<br>
                        <span class="seasonal-indicator seasonal-medium"></span> Medium Season (€10k-€15k)<br>
                        <span class="seasonal-indicator seasonal-low"></span> Low Season (<€10k)
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Cash Flow Table -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> Detailed Monthly Cash Flow</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Month</th>
                                    <th>Revenue</th>
                                    <th>Payments Received</th>
                                    <th>Net Cash Flow</th>
                                    <th>Cumulative</th>
                                    <th>Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for month in monthly_cash_flow %}
                                <tr>
                                    <td><strong>{{ month.month|date:"M Y" }}</strong></td>
                                    <td>€{{ month.revenue|floatformat:0|intcomma }}</td>
                                    <td>€{{ month.payments|floatformat:0|intcomma }}</td>
                                    <td class="{% if month.net_cash_flow >= 0 %}cashflow-positive{% else %}cashflow-negative{% endif %}">
                                        €{{ month.net_cash_flow|floatformat:0|intcomma }}
                                    </td>
                                    <td class="{% if month.cumulative_cash_flow >= 0 %}cashflow-positive{% else %}cashflow-negative{% endif %}">
                                        €{{ month.cumulative_cash_flow|floatformat:0|intcomma }}
                                    </td>
                                    <td><span class="badge bg-primary">Historical</span></td>
                                </tr>
                                {% endfor %}

                                {% for forecast in forecast_months %}
                                <tr class="table-info">
                                    <td><strong>{{ forecast.month|date:"M Y" }}</strong></td>
                                    <td>€{{ forecast.revenue|floatformat:0|intcomma }}</td>
                                    <td>€{{ forecast.payments|floatformat:0|intcomma }}</td>
                                    <td class="{% if forecast.net_cash_flow >= 0 %}cashflow-positive{% else %}cashflow-negative{% endif %}">
                                        €{{ forecast.net_cash_flow|floatformat:0|intcomma }}
                                    </td>
                                    <td>-</td>
                                    <td><span class="badge bg-info">Forecast</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cash Flow Insights and Recommendations -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> Payment Method Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="paymentMethodChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Recommendations -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> Cash Flow Insights</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Key Insights</h6>
                        <ul class="mb-0">
                            <li>{{ payment_trends|length }} payment methods in use</li>
                            <li>Seasonal patterns identified</li>
                            <li>6-month forecast available</li>
                            <li>Historical trends analyzed</li>
                        </ul>
                    </div>

                    {% if monthly_cash_flow|last|get_item:"net_cash_flow" < 0 %}
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Action Required</h6>
                        <p class="mb-0">Current month shows negative cash flow. Monitor payment collections closely.</p>
                    </div>
                    {% endif %}

                    <div class="alert alert-success">
                        <h6><i class="fas fa-target"></i> Recommendations</h6>
                        <ul class="mb-0">
                            <li>Monitor cash flow weekly</li>
                            <li>Improve payment collection</li>
                            <li>Plan for seasonal variations</li>
                            <li>Maintain cash reserves</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6>Export Options</h6>
                    <a href="#" class="btn btn-success me-2" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> Export Forecast to Excel
                    </a>
                    <a href="#" class="btn btn-danger" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i> Export Report to PDF
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script>
// Cash Flow Chart
const cashFlowCtx = document.getElementById('cashFlowChart').getContext('2d');
const cashFlowChart = new Chart(cashFlowCtx, {
    type: 'line',
    data: {
        labels: [
            {% for month in monthly_cash_flow %}
            '{{ month.month|date:"M Y" }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
            {% if forecast_months %}
            {% for forecast in forecast_months %}
            ,'{{ forecast.month|date:"M Y" }}'
            {% endfor %}
            {% endif %}
        ],
        datasets: [{
            label: 'Historical Net Cash Flow',
            data: [
                {% for month in monthly_cash_flow %}
                {{ month.net_cash_flow|default:0 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
                {% if forecast_months %}
                {% for forecast in forecast_months %}
                ,null
                {% endfor %}
                {% endif %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            fill: false
        }, {
            label: 'Forecast Net Cash Flow',
            data: [
                {% for month in monthly_cash_flow %}
                null{% if not forloop.last %},{% endif %}
                {% endfor %}
                {% if forecast_months %}
                {% for forecast in forecast_months %}
                ,{{ forecast.net_cash_flow|default:0 }}
                {% endfor %}
                {% endif %}
            ],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            borderDash: [5, 5],
            tension: 0.1,
            fill: false
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                title: {
                    display: true,
                    text: 'Cash Flow (€)'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'Time Period'
                }
            }
        },
        plugins: {
            legend: {
                display: true
            }
        }
    }
});

// Payment Method Chart
const paymentMethodCtx = document.getElementById('paymentMethodChart').getContext('2d');
const paymentMethodChart = new Chart(paymentMethodCtx, {
    type: 'doughnut',
    data: {
        labels: [
            {% for payment in payment_trends %}
            '{{ payment.payment_method|title }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            data: [
                {% for payment in payment_trends %}
                {{ payment.total_amount|default:0 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                'rgba(255, 99, 132, 0.8)',
                'rgba(54, 162, 235, 0.8)',
                'rgba(255, 205, 86, 0.8)',
                'rgba(75, 192, 192, 0.8)',
                'rgba(153, 102, 255, 0.8)',
                'rgba(255, 159, 64, 0.8)'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

function exportToExcel() {
    alert('Excel export functionality will be implemented');
}

function exportToPDF() {
    alert('PDF export functionality will be implemented');
}
</script>
{% endblock %}
