#!/usr/bin/env python3
"""
Test script for IFRS/SKK compliant inventory costing implementation
"""

import os
import django
from decimal import Decimal

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

from items.models import (
    RawMaterial, RawMaterialInventory, RawMaterialInventoryHistory,
    RawMaterialInventoryBatch, Currency, Unit, Item, ItemRawMaterial
)
from billing.models import PurchaseOrder, PurchaseOrderItem, Supplier
from items.services.inventory_costing import InventoryCostingService


def test_ifrs_costing_implementation():
    print("🧪 TESTING IFRS/SKK COMPLIANT INVENTORY COSTING")
    print("=" * 60)

    # Test 1: Weighted Average Cost Method
    print("\n1️⃣ TESTING WEIGHTED AVERAGE COST (WAC) METHOD")
    print("-" * 50)

    try:
        # Get test data
        zirconia = RawMaterial.objects.filter(name__icontains='zirconia').first()
        supplier = Supplier.objects.first()
        usd = Currency.objects.filter(code='USD').first()
        gram = Unit.objects.filter(name__icontains='gram').first()

        if not all([zirconia, supplier, usd, gram]):
            print("❌ Missing test data. Please run populate_items_only.py first")
            return False

        print(f"📦 Testing with material: {zirconia.name}")
        print(f"🏪 Supplier: {supplier.name}")

        # Create purchase order
        po = PurchaseOrder.objects.create(
            supplier=supplier,
            order_date='2024-01-01',
            status='draft'
        )

        # Test multiple purchases with different prices
        purchases = [
            {'quantity': Decimal('100'), 'price': Decimal('75.00')},
            {'quantity': Decimal('150'), 'price': Decimal('80.00')},
            {'quantity': Decimal('200'), 'price': Decimal('85.00')},
        ]

        costing_service = InventoryCostingService()

        for i, purchase in enumerate(purchases, 1):
            print(f"\n📥 Purchase {i}: {purchase['quantity']} units @ ${purchase['price']}/unit")

            # Create purchase order item
            po_item = PurchaseOrderItem.objects.create(
                purchase_order=po,
                raw_material=zirconia,
                quantity=purchase['quantity'],
                unit=gram,
                price_per_unit=purchase['price'],
                currency=usd
            )

            # Check inventory after purchase (get the one with the correct unit)
            inventory = RawMaterialInventory.objects.get(raw_material=zirconia, unit=gram)
            print(f"   📊 Total Quantity: {inventory.quantity}")
            print(f"   💰 WAC: ${inventory.weighted_average_cost}")
            print(f"   💵 Total Value: ${inventory.total_cost_basis}")

        # Calculate expected WAC
        total_cost = sum(p['quantity'] * p['price'] for p in purchases)
        total_quantity = sum(p['quantity'] for p in purchases)
        expected_wac = total_cost / total_quantity

        print(f"\n✅ Expected WAC: ${expected_wac:.4f}")
        print(f"✅ Actual WAC: ${inventory.weighted_average_cost}")
        print(f"✅ Match: {abs(expected_wac - inventory.weighted_average_cost) < Decimal('0.0001')}")

    except Exception as e:
        print(f"❌ Error in WAC test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

    # Test 2: Item Cost Calculation
    print("\n\n2️⃣ TESTING ITEM COST CALCULATION")
    print("-" * 50)

    try:
        # Get or create a test item
        crown = Item.objects.filter(name__icontains='crown').first()
        if not crown:
            print("❌ No crown item found for testing")
            return False

        print(f"👑 Testing item: {crown.name}")

        # Get cost using old method (base price)
        old_cost = Decimal('0')
        for irm in crown.itemrawmaterial_set.all():
            old_cost += irm.raw_material.price_per_unit * irm.quantity

        # Get cost using new IFRS/SKK method
        new_cost = crown.cost()

        print(f"💰 Old Cost (Base Price): ${old_cost:.2f}")
        print(f"💰 New Cost (IFRS/SKK): ${new_cost:.2f}")
        print(f"📊 Difference: ${abs(new_cost - old_cost):.2f}")

        # Get detailed cost breakdown
        breakdown = crown.cost_breakdown()
        print(f"\n📋 Cost Breakdown:")
        for item in breakdown:
            print(f"   • {item['material_name']}: {item['quantity']} {item['unit']} @ ${item['unit_cost']:.4f} = ${item['total_cost']:.2f}")
            print(f"     Method: {item['costing_method']}")

    except Exception as e:
        print(f"❌ Error in item cost test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

    # Test 3: Inventory Valuation
    print("\n\n3️⃣ TESTING INVENTORY VALUATION")
    print("-" * 50)

    try:
        total_value = costing_service.calculate_inventory_value()
        print(f"💎 Total Inventory Value: ${total_value:.2f}")

        # Test individual material valuation
        zirconia_value = costing_service.calculate_inventory_value(zirconia)
        print(f"📦 Zirconia Inventory Value: ${zirconia_value:.2f}")

        # Check inventory records
        inventories = RawMaterialInventory.objects.all()
        print(f"\n📊 Inventory Summary:")
        for inv in inventories[:5]:  # Show first 5
            print(f"   • {inv.raw_material.name}: {inv.quantity} units")
            print(f"     Method: {inv.get_costing_method_display()}")
            print(f"     Value: ${inv.get_inventory_value():.2f}")

    except Exception as e:
        print(f"❌ Error in valuation test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

    # Test 4: History Tracking
    print("\n\n4️⃣ TESTING HISTORY TRACKING")
    print("-" * 50)

    try:
        history_records = RawMaterialInventoryHistory.objects.filter(
            raw_material_inventory__raw_material=zirconia
        ).order_by('-timestamp')[:3]

        print(f"📜 Recent History for {zirconia.name}:")
        for record in history_records:
            print(f"   • {record.timestamp.strftime('%Y-%m-%d %H:%M')}: {record.change_type}")
            print(f"     Quantity: {record.change_quantity}")
            print(f"     Unit Cost: ${record.unit_cost:.4f}")
            print(f"     Total Cost: ${record.total_cost:.2f}")
            if record.new_weighted_average_cost:
                print(f"     New WAC: ${record.new_weighted_average_cost:.4f}")

    except Exception as e:
        print(f"❌ Error in history test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

    print("\n\n✅ ALL TESTS COMPLETED SUCCESSFULLY!")
    print("🎯 IFRS/SKK Compliant Inventory Costing is working correctly")

    return True


def test_costing_methods_comparison():
    """Compare different costing methods with the same data"""
    print("\n\n5️⃣ COSTING METHODS COMPARISON")
    print("-" * 50)

    # Example data
    purchases = [
        {'date': '2024-01-01', 'qty': 100, 'price': 75.00},
        {'date': '2024-01-15', 'qty': 150, 'price': 80.00},
        {'date': '2024-01-30', 'qty': 200, 'price': 85.00},
    ]

    consumption = 180  # Units consumed

    print("📚 Example: Zirconia Powder Inventory")
    print("Purchases:")
    total_cost = 0
    total_qty = 0
    for p in purchases:
        cost = p['qty'] * p['price']
        total_cost += cost
        total_qty += p['qty']
        print(f"   • {p['date']}: {p['qty']} kg @ ${p['price']}/kg = ${cost:,.2f}")

    print(f"   Total: {total_qty} kg @ ${total_cost/total_qty:.2f}/kg = ${total_cost:,.2f}")
    print(f"\nConsumption: {consumption} kg")

    # FIFO Calculation
    fifo_cost = 0
    remaining_qty = consumption
    remaining_inventory = []

    for p in purchases:
        if remaining_qty <= 0:
            remaining_inventory.append(p)
            continue

        consumed_from_batch = min(remaining_qty, p['qty'])
        fifo_cost += consumed_from_batch * p['price']
        remaining_qty -= consumed_from_batch

        if p['qty'] > consumed_from_batch:
            remaining_inventory.append({
                'qty': p['qty'] - consumed_from_batch,
                'price': p['price']
            })

    # WAC Calculation
    wac_price = total_cost / total_qty
    wac_cost = consumption * wac_price

    print(f"\n🔹 FIFO (First In, First Out):")
    print(f"   Cost of {consumption} kg = ${fifo_cost:,.2f}")

    print(f"\n🔹 Weighted Average Cost:")
    print(f"   Cost of {consumption} kg = {consumption} kg @ ${wac_price:.2f}/kg = ${wac_cost:,.2f}")

    print(f"\n📊 Difference: ${abs(fifo_cost - wac_cost):.2f}")


if __name__ == "__main__":
    success = test_ifrs_costing_implementation()
    if success:
        test_costing_methods_comparison()
    else:
        print("❌ Tests failed. Please check the implementation.")
