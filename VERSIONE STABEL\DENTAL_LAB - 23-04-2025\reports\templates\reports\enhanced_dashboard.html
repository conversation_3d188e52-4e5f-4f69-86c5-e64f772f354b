{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}Enhanced Executive Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
<style>
    .kpi-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    .kpi-card:hover {
        transform: translateY(-5px);
    }
    .kpi-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .kpi-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .kpi-change {
        font-size: 0.8rem;
        margin-top: 5px;
    }
    .kpi-change.positive {
        color: #4CAF50;
    }
    .kpi-change.negative {
        color: #f44336;
    }
    .chart-container {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .alert-item {
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 5px;
        border-left: 4px solid;
    }
    .alert-high {
        background-color: #ffebee;
        border-left-color: #f44336;
    }
    .alert-medium {
        background-color: #fff3e0;
        border-left-color: #ff9800;
    }
    .alert-low {
        background-color: #e8f5e8;
        border-left-color: #4caf50;
    }
    .top-performer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 5px;
    }
    .recent-activity {
        max-height: 300px;
        overflow-y: auto;
    }
    .activity-item {
        padding: 8px;
        border-bottom: 1px solid #eee;
    }
    .activity-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-chart-line"></i> Enhanced Executive Dashboard
            </h1>
            
            <!-- Date Range Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="fas fa-filter"></i> Apply Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- KPI Cards Row -->
    <div class="row">
        <!-- Financial KPIs -->
        <div class="col-lg-3 col-md-6">
            <div class="kpi-card">
                <div class="kpi-value">€{{ current_revenue|floatformat:0|intcomma }}</div>
                <div class="kpi-label">Total Revenue</div>
                <div class="kpi-change {% if revenue_change >= 0 %}positive{% else %}negative{% endif %}">
                    <i class="fas fa-{% if revenue_change >= 0 %}arrow-up{% else %}arrow-down{% endif %}"></i>
                    {{ revenue_change|floatformat:1 }}% vs previous period
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="kpi-card">
                <div class="kpi-value">{{ current_case_count }}</div>
                <div class="kpi-label">Cases Processed</div>
                <div class="kpi-change {% if case_volume_change >= 0 %}positive{% else %}negative{% endif %}">
                    <i class="fas fa-{% if case_volume_change >= 0 %}arrow-up{% else %}arrow-down{% endif %}"></i>
                    {{ case_volume_change|floatformat:1 }}% vs previous period
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="kpi-card">
                <div class="kpi-value">{{ completion_rate|floatformat:1 }}%</div>
                <div class="kpi-label">Completion Rate</div>
                <div class="kpi-change {% if completion_rate_change >= 0 %}positive{% else %}negative{% endif %}">
                    <i class="fas fa-{% if completion_rate_change >= 0 %}arrow-up{% else %}arrow-down{% endif %}"></i>
                    {{ completion_rate_change|floatformat:1 }}% change
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="kpi-card">
                <div class="kpi-value">{{ avg_cycle_days }}</div>
                <div class="kpi-label">Avg Cycle Time (Days)</div>
                <div class="kpi-change">
                    <i class="fas fa-clock"></i>
                    {{ overdue_cases }} overdue cases
                </div>
            </div>
        </div>
    </div>

    <!-- Secondary KPIs Row -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="kpi-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="kpi-value">{{ payment_rate|floatformat:1 }}%</div>
                <div class="kpi-label">Payment Rate</div>
                <div class="kpi-change">
                    €{{ outstanding_balance|floatformat:0|intcomma }} outstanding
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="kpi-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="kpi-value">{{ avg_patient_feedback|floatformat:1 }}/5</div>
                <div class="kpi-label">Patient Satisfaction</div>
                <div class="kpi-change">
                    {{ revision_rate|floatformat:1 }}% revision rate
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="kpi-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="kpi-value">{{ stock_health|floatformat:1 }}%</div>
                <div class="kpi-label">Inventory Health</div>
                <div class="kpi-change">
                    {{ low_stock_items }} items low stock
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6">
            <div class="kpi-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="kpi-value">€{{ total_inventory_value|floatformat:0|intcomma }}</div>
                <div class="kpi-label">Inventory Value</div>
                <div class="kpi-change">
                    {{ total_items }} total items
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Analytics Row -->
    <div class="row">
        <!-- Monthly Trends Chart -->
        <div class="col-lg-8">
            <div class="chart-container">
                <h5><i class="fas fa-chart-line"></i> Monthly Revenue & Case Trends</h5>
                <canvas id="monthlyTrendsChart" height="100"></canvas>
            </div>
        </div>
        
        <!-- Top Performers -->
        <div class="col-lg-4">
            <div class="chart-container">
                <h5><i class="fas fa-trophy"></i> Top Performing Dentists</h5>
                {% for dentist in top_dentists %}
                <div class="top-performer">
                    <div>
                        <strong>{{ dentist.full_name }}</strong><br>
                        <small>{{ dentist.case_count }} cases</small>
                    </div>
                    <div class="text-end">
                        <strong>€{{ dentist.total_revenue|floatformat:0|intcomma }}</strong>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted">No data available for the selected period.</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Recent Activities and Quick Actions -->
    <div class="row">
        <!-- Recent Cases -->
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-clock"></i> Recent Cases</h5>
                <div class="recent-activity">
                    {% for case in recent_cases %}
                    <div class="activity-item">
                        <div class="d-flex justify-content-between">
                            <div>
                                <strong>{{ case.case_number }}</strong> - {{ case.dentist.first_name }} {{ case.dentist.last_name }}<br>
                                <small class="text-muted">{{ case.patient.first_name }} {{ case.patient.last_name }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-{% if case.status == 'completed' %}success{% elif case.status == 'in_progress' %}warning{% else %}secondary{% endif %}">
                                    {{ case.status|title }}
                                </span><br>
                                <small class="text-muted">{{ case.received_date_time|date:"M d, Y" }}</small>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">No recent cases found.</p>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="col-lg-6">
            <div class="chart-container">
                <h5><i class="fas fa-bolt"></i> Quick Actions & Reports</h5>
                <div class="row">
                    <div class="col-6 mb-3">
                        <a href="{% url 'profitability_analysis_report' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-chart-pie"></i><br>
                            Profitability Analysis
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{% url 'quality_metrics_dashboard' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-star"></i><br>
                            Quality Metrics
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{% url 'workflow_efficiency_analysis' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-cogs"></i><br>
                            Workflow Efficiency
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{% url 'inventory_optimization_report' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-boxes"></i><br>
                            Inventory Optimization
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{% url 'demand_forecasting_report' %}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-crystal-ball"></i><br>
                            Demand Forecasting
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="{% url 'customer_relationship_analysis' %}" class="btn btn-outline-dark w-100">
                            <i class="fas fa-users"></i><br>
                            Customer Analysis
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script>
// Monthly Trends Chart
const monthlyTrendsCtx = document.getElementById('monthlyTrendsChart').getContext('2d');
const monthlyTrendsChart = new Chart(monthlyTrendsCtx, {
    type: 'line',
    data: {
        labels: [
            {% for trend in monthly_trends %}
            '{{ trend.month|date:"M Y" }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Revenue (€)',
            data: [
                {% for trend in monthly_trends %}
                {{ trend.revenue|default:0 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            yAxisID: 'y'
        }, {
            label: 'Cases',
            data: [
                {% for trend in monthly_trends %}
                {{ trend.case_count|default:0 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            x: {
                display: true,
                title: {
                    display: true,
                    text: 'Month'
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Revenue (€)'
                }
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Cases'
                },
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});

// Auto-refresh every 5 minutes
setTimeout(function() {
    location.reload();
}, 300000);
</script>
{% endblock %}
