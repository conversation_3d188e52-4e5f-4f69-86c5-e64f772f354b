# Generated by Django 5.2.1 on 2025-05-24 22:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('items', '0003_populate_inventory_costing_data'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='rawmaterialinventorybatch',
            options={'ordering': ['purchase_date'], 'verbose_name': 'Raw Material Inventory Batch', 'verbose_name_plural': 'Raw Material Inventory Batches'},
        ),
        migrations.AlterField(
            model_name='rawmaterialinventory',
            name='currency',
            field=models.ForeignKey(blank=True, help_text='Currency for cost calculations', null=True, on_delete=django.db.models.deletion.CASCADE, to='items.currency'),
        ),
        migrations.AlterField(
            model_name='rawmaterialinventoryhistory',
            name='currency',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='items.currency'),
        ),
    ]
