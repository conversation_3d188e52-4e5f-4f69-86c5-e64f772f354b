{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}Quality Metrics Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
<style>
    .quality-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    .quality-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .quality-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .alert-high {
        background-color: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }
    .alert-medium {
        background-color: #fff3cd;
        border-color: #ffeaa7;
        color: #856404;
    }
    .quality-excellent {
        background-color: #d4edda;
        color: #155724;
    }
    .quality-good {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    .quality-poor {
        background-color: #f8d7da;
        color: #721c24;
    }
    .dentist-quality-item {
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 5px;
        border-left: 4px solid;
    }
    .dentist-excellent {
        background-color: #d4edda;
        border-left-color: #28a745;
    }
    .dentist-good {
        background-color: #d1ecf1;
        border-left-color: #17a2b8;
    }
    .dentist-needs-improvement {
        background-color: #f8d7da;
        border-left-color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-star"></i> Quality Metrics Dashboard
            </h1>
            
            <!-- Date Range Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="fas fa-filter"></i> Apply Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Quality KPIs -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="quality-card">
                <div class="quality-value">{{ total_cases }}</div>
                <div class="quality-label">Total Cases</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="quality-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="quality-value">{{ revision_rate|floatformat:1 }}%</div>
                <div class="quality-label">Revision Rate</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="quality-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="quality-value">{{ avg_patient_feedback|floatformat:1 }}/5</div>
                <div class="quality-label">Patient Satisfaction</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="quality-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="quality-value">{{ avg_dentist_feedback|floatformat:1 }}/5</div>
                <div class="quality-label">Dentist Satisfaction</div>
            </div>
        </div>
    </div>

    <!-- Quality Alerts -->
    {% if quality_alerts %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle"></i> Quality Alerts</h5>
                </div>
                <div class="card-body">
                    {% for alert in quality_alerts %}
                    <div class="alert alert-{{ alert.severity }} d-flex justify-content-between align-items-center">
                        <div>
                            <strong>{{ alert.type }}</strong> - {{ alert.dentist }}
                        </div>
                        <div>
                            <span class="badge bg-{{ alert.severity }}">{{ alert.value|floatformat:1 }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Quality Trends and Dentist Performance -->
    <div class="row">
        <!-- Quality Trends Chart -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Quality Trends Over Time</h5>
                </div>
                <div class="card-body">
                    <canvas id="qualityTrendsChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Top Quality Performers -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-trophy"></i> Quality Leaders</h5>
                </div>
                <div class="card-body">
                    {% for dentist in dentist_quality|slice:":5" %}
                    <div class="dentist-quality-item 
                        {% if dentist.revision_rate <= 5 %}dentist-excellent
                        {% elif dentist.revision_rate <= 15 %}dentist-good
                        {% else %}dentist-needs-improvement{% endif %}">
                        <div class="d-flex justify-content-between">
                            <div>
                                <strong>{{ dentist.full_name }}</strong><br>
                                <small>{{ dentist.case_count }} cases</small>
                            </div>
                            <div class="text-end">
                                <strong>{{ dentist.revision_rate|floatformat:1 }}%</strong><br>
                                <small>revision rate</small>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">No quality data available.</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Quality Analysis -->
    <div class="row mt-4">
        <!-- Dentist Quality Performance -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-users"></i> Dentist Quality Performance</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Dentist</th>
                                    <th>Cases</th>
                                    <th>Revisions</th>
                                    <th>Rev. Rate</th>
                                    <th>Patient Sat.</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dentist in dentist_quality %}
                                <tr>
                                    <td>{{ dentist.full_name }}</td>
                                    <td>{{ dentist.case_count }}</td>
                                    <td>{{ dentist.total_revisions|default:0 }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if dentist.revision_rate <= 5 %}bg-success
                                            {% elif dentist.revision_rate <= 15 %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ dentist.revision_rate|floatformat:1 }}%
                                        </span>
                                    </td>
                                    <td>
                                        {% if dentist.avg_patient_feedback %}
                                        <span class="badge 
                                            {% if dentist.avg_patient_feedback >= 4 %}bg-success
                                            {% elif dentist.avg_patient_feedback >= 3 %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ dentist.avg_patient_feedback|floatformat:1 }}/5
                                        </span>
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quality by Item Type -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-cogs"></i> Quality by Item Type</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Item Type</th>
                                    <th>Cases</th>
                                    <th>Rev. Rate</th>
                                    <th>Avg. Rating</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in item_quality %}
                                <tr>
                                    <td>{{ item.item__name }}</td>
                                    <td>{{ item.case_count }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if item.revision_rate <= 5 %}bg-success
                                            {% elif item.revision_rate <= 15 %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ item.revision_rate|floatformat:1 }}%
                                        </span>
                                    </td>
                                    <td>
                                        {% if item.avg_patient_feedback %}
                                        {{ item.avg_patient_feedback|floatformat:1 }}/5
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">No data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quality Improvement Recommendations -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> Quality Improvement Recommendations</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Immediate Actions</h6>
                            <ul>
                                {% if revision_rate > 15 %}
                                <li class="text-danger">Overall revision rate is high ({{ revision_rate|floatformat:1 }}%). Review quality control processes.</li>
                                {% endif %}
                                {% if avg_patient_feedback < 3.5 %}
                                <li class="text-warning">Patient satisfaction is below target. Investigate common issues.</li>
                                {% endif %}
                                {% for alert in quality_alerts %}
                                {% if alert.severity == 'high' %}
                                <li class="text-danger">Address {{ alert.type|lower }} for {{ alert.dentist }}</li>
                                {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Long-term Improvements</h6>
                            <ul>
                                <li>Implement regular quality training sessions</li>
                                <li>Establish quality benchmarks and targets</li>
                                <li>Create feedback loops with dentists</li>
                                <li>Monitor quality trends monthly</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script>
// Quality Trends Chart
const qualityTrendsCtx = document.getElementById('qualityTrendsChart').getContext('2d');
const qualityTrendsChart = new Chart(qualityTrendsCtx, {
    type: 'line',
    data: {
        labels: [
            {% for trend in monthly_quality %}
            '{{ trend.month|date:"M Y" }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Revision Rate (%)',
            data: [
                {% for trend in monthly_quality %}
                {{ trend.revision_rate|default:0 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1,
            yAxisID: 'y'
        }, {
            label: 'Patient Satisfaction',
            data: [
                {% for trend in monthly_quality %}
                {{ trend.avg_patient_feedback|default:0 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            yAxisID: 'y1'
        }]
    },
    options: {
        responsive: true,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        scales: {
            x: {
                display: true,
                title: {
                    display: true,
                    text: 'Month'
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Revision Rate (%)'
                },
                max: 30
            },
            y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                    display: true,
                    text: 'Patient Satisfaction (1-5)'
                },
                min: 0,
                max: 5,
                grid: {
                    drawOnChartArea: false,
                },
            }
        }
    }
});
</script>
{% endblock %}
