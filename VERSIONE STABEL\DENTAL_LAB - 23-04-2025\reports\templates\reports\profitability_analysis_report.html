{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}Profitability Analysis Report{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    .metric-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .metric-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .profit-table {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .profit-positive {
        color: #28a745;
    }
    .profit-negative {
        color: #dc3545;
    }
    .margin-excellent {
        background-color: #d4edda;
        color: #155724;
    }
    .margin-good {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    .margin-poor {
        background-color: #f8d7da;
        color: #721c24;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-chart-pie"></i> Profitability Analysis Report
            </h1>
            
            <!-- Date Range Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="fas fa-filter"></i> Apply Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Metrics -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="metric-card">
                <div class="metric-value">€{{ total_revenue|floatformat:0|intcomma }}</div>
                <div class="metric-label">Total Revenue</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="metric-value">€{{ total_cost|floatformat:0|intcomma }}</div>
                <div class="metric-label">Total Cost</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="metric-value">€{{ total_profit|floatformat:0|intcomma }}</div>
                <div class="metric-label">Total Profit</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="metric-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="metric-value">{{ overall_margin|floatformat:1 }}%</div>
                <div class="metric-label">Overall Margin</div>
            </div>
        </div>
    </div>

    <!-- Break-even Analysis -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-balance-scale"></i> Break-even Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <h6>Break-even Revenue</h6>
                            <h4 class="text-primary">€{{ break_even_revenue|floatformat:0|intcomma }}</h4>
                        </div>
                        <div class="col-6">
                            <h6>Current vs Break-even</h6>
                            {% if total_revenue > break_even_revenue %}
                                <h4 class="text-success">
                                    <i class="fas fa-check-circle"></i> Above Break-even
                                </h4>
                            {% else %}
                                <h4 class="text-warning">
                                    <i class="fas fa-exclamation-triangle"></i> Below Break-even
                                </h4>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Top/Bottom Performers -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-trophy"></i> Performance Highlights</h5>
                </div>
                <div class="card-body">
                    <h6>Top Performers</h6>
                    {% for item in top_performers %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>{{ item.item_name }}</span>
                        <span class="profit-positive">{{ item.margin_percentage|floatformat:1 }}%</span>
                    </div>
                    {% endfor %}
                    
                    {% if bottom_performers %}
                    <hr>
                    <h6>Needs Attention</h6>
                    {% for item in bottom_performers %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>{{ item.item_name }}</span>
                        <span class="profit-negative">{{ item.margin_percentage|floatformat:1 }}%</span>
                    </div>
                    {% endfor %}
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Profitability Table -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card profit-table">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> Detailed Profitability Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Item/Service</th>
                                    <th>Quantity Sold</th>
                                    <th>Revenue</th>
                                    <th>Cost</th>
                                    <th>Profit</th>
                                    <th>Margin %</th>
                                    <th>ROI %</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in profitability_data %}
                                <tr>
                                    <td><strong>{{ item.item_name }}</strong></td>
                                    <td>{{ item.quantity_sold }}</td>
                                    <td>€{{ item.revenue|floatformat:2|intcomma }}</td>
                                    <td>€{{ item.cost|floatformat:2|intcomma }}</td>
                                    <td class="{% if item.profit >= 0 %}profit-positive{% else %}profit-negative{% endif %}">
                                        €{{ item.profit|floatformat:2|intcomma }}
                                    </td>
                                    <td>
                                        <span class="badge 
                                            {% if item.margin_percentage >= 30 %}bg-success
                                            {% elif item.margin_percentage >= 15 %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ item.margin_percentage|floatformat:1 }}%
                                        </span>
                                    </td>
                                    <td>{{ item.roi|floatformat:1 }}%</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted">
                                        No profitability data available for the selected period.
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profitability Chart -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> Profit Margin by Item</h5>
                </div>
                <div class="card-body">
                    <canvas id="profitabilityChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> Recommendations</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Key Insights</h6>
                        <ul class="mb-0">
                            <li>Focus on high-margin items (>30%)</li>
                            <li>Review pricing for low-margin items</li>
                            <li>Consider cost reduction strategies</li>
                            <li>Promote profitable services</li>
                        </ul>
                    </div>
                    
                    {% if overall_margin < 20 %}
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Action Required</h6>
                        <p class="mb-0">Overall margin is below 20%. Consider reviewing pricing strategy and cost structure.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6>Export Options</h6>
                    <a href="#" class="btn btn-success me-2" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> Export to Excel
                    </a>
                    <a href="#" class="btn btn-danger" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i> Export to PDF
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script>
// Profitability Chart
const profitabilityCtx = document.getElementById('profitabilityChart').getContext('2d');
const profitabilityChart = new Chart(profitabilityCtx, {
    type: 'bar',
    data: {
        labels: [
            {% for item in profitability_data|slice:":10" %}
            '{{ item.item_name|truncatechars:15 }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Profit Margin (%)',
            data: [
                {% for item in profitability_data|slice:":10" %}
                {{ item.margin_percentage|default:0 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                {% for item in profitability_data|slice:":10" %}
                {% if item.margin_percentage >= 30 %}'rgba(75, 192, 192, 0.8)'
                {% elif item.margin_percentage >= 15 %}'rgba(255, 206, 86, 0.8)'
                {% else %}'rgba(255, 99, 132, 0.8)'{% endif %}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: [
                {% for item in profitability_data|slice:":10" %}
                {% if item.margin_percentage >= 30 %}'rgba(75, 192, 192, 1)'
                {% elif item.margin_percentage >= 15 %}'rgba(255, 206, 86, 1)'
                {% else %}'rgba(255, 99, 132, 1)'{% endif %}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Profit Margin (%)'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'Items/Services'
                }
            }
        },
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    afterLabel: function(context) {
                        const item = {{ profitability_data|slice:":10"|safe }};
                        if (item[context.dataIndex]) {
                            return [
                                'Revenue: €' + item[context.dataIndex].revenue.toLocaleString(),
                                'Profit: €' + item[context.dataIndex].profit.toLocaleString(),
                                'ROI: ' + item[context.dataIndex].roi.toFixed(1) + '%'
                            ];
                        }
                        return [];
                    }
                }
            }
        }
    }
});

function exportToExcel() {
    // Implementation for Excel export
    alert('Excel export functionality will be implemented');
}

function exportToPDF() {
    // Implementation for PDF export
    alert('PDF export functionality will be implemented');
}
</script>
{% endblock %}
