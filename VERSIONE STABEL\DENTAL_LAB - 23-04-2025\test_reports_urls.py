#!/usr/bin/env python
"""
Test script to verify all reports URLs are working correctly.
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

from django.urls import reverse, NoReverseMatch
from django.test import Client
from django.contrib.auth.models import User

def test_reports_urls():
    """Test all reports URLs to ensure they're properly configured."""
    
    # List of all report URL names
    report_urls = [
        'reports_dashboard',
        'profitability_analysis_report',
        'cash_flow_forecast_report',
        'quality_metrics_dashboard',
        'workflow_efficiency_analysis',
        'inventory_optimization_report',
        'demand_forecasting_report',
        'customer_relationship_analysis',
        # Existing reports
        'cycletimes',
        'revenue_report',
        'case_volume_report',
        'case_status_report',
        'dentist_leaderboard',
        'appointment_scheduling_report',
        'item_usage_report',
        'item_inventory_report',
        'financial_report',
        'detailed_financial_report',
        'case_progress_report',
        'inventory_management_report',
        'dentist_performance_report',
        'dentist_financial_report',
        'dentist_financial_summary',
        'dentist_treemap_chart',
    ]
    
    print("Testing Reports URLs...")
    print("=" * 50)
    
    # Test URL reverse lookup
    working_urls = []
    broken_urls = []
    
    for url_name in report_urls:
        try:
            url = reverse(url_name)
            working_urls.append((url_name, url))
            print(f"✅ {url_name:<35} -> {url}")
        except NoReverseMatch as e:
            broken_urls.append((url_name, str(e)))
            print(f"❌ {url_name:<35} -> ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"SUMMARY:")
    print(f"✅ Working URLs: {len(working_urls)}")
    print(f"❌ Broken URLs:  {len(broken_urls)}")
    
    if broken_urls:
        print("\nBROKEN URLS:")
        for url_name, error in broken_urls:
            print(f"  - {url_name}: {error}")
        return False
    else:
        print("\n🎉 All URLs are working correctly!")
        return True

if __name__ == "__main__":
    success = test_reports_urls()
    sys.exit(0 if success else 1)
