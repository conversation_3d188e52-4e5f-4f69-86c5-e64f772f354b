# IFRS/SKK Compliant Inventory Costing - Detailed Implementation Plan

## 📊 Current System Analysis

### Existing Models and Their Roles:

#### 1. **RawMaterial Model** (`items/models.py:118-161`)
```python
# Current fields:
- name, description, unit, suppliers (M2M through SupplierRawMaterial)
- price_per_unit, currency  # ❌ Static base price - PROBLEM
- is_active, category, reorder_point
- created_at, updated_at

# Current methods:
- get_current_stock() # ✅ Works correctly
- needs_reorder()     # ✅ Works correctly
```

#### 2. **RawMaterialInventory Model** (`items/models.py:270-278`)
```python
# Current fields:
- raw_material, quantity, unit, location, last_updated

# ❌ MISSING COSTING FIELDS:
- No cost basis tracking
- No costing method specification
- No weighted average cost
- No currency for inventory valuation
```

#### 3. **RawMaterialInventoryHistory Model** (`items/models.py:280-295`)
```python
# Current fields:
- raw_material_inventory, change_type, change_quantity
- purchase_order_item, case_item, timestamp

# ❌ MISSING COSTING FIELDS:
- No unit cost tracking
- No total cost tracking
- No currency tracking
```

#### 4. **PurchaseOrderItem Model** (`billing/models.py:733-788`)
```python
# Current fields: ✅ GOOD FOR COSTING
- purchase_order, raw_material, description
- quantity, unit, price_per_unit, currency  # ✅ Has actual purchase costs
```

#### 5. **Item.cost() Method** (`items/models.py:201-222`)
```python
# ❌ CURRENT PROBLEM:
def cost(self):
    for irm in raw_materials:
        cost_in_raw_currency = irm.raw_material.price_per_unit * irm.quantity  # Uses base price!
```

### Current Inventory Flow:

#### Purchase Flow:
1. **PurchaseOrderItem created** → `items/signals.py:113-142`
2. **RawMaterialInventory updated** → Quantity increased
3. **RawMaterialInventoryHistory created** → Records transaction
4. **❌ NO COST BASIS TRACKING**

#### Consumption Flow:
1. **CaseItem created** → Material consumed for production
2. **❌ NO AUTOMATIC INVENTORY REDUCTION**
3. **❌ NO COST OF GOODS USED TRACKING**

## 🎯 Implementation Strategy

### Phase 1: Database Schema Enhancement

#### 1.1 Enhance RawMaterialInventory Model
```python
# Add to items/models.py
class RawMaterialInventory(models.Model):
    # Existing fields...
    raw_material = models.ForeignKey(RawMaterial, on_delete=models.CASCADE)
    quantity = models.DecimalField(max_digits=10, decimal_places=2)
    unit = models.ForeignKey(Unit, on_delete=models.CASCADE)
    location = models.CharField(max_length=255, blank=True, null=True)
    last_updated = models.DateTimeField(auto_now=True)
    
    # NEW COSTING FIELDS
    costing_method = models.CharField(
        max_length=20,
        choices=[
            ('WAC', 'Weighted Average Cost'),
            ('SPECIFIC', 'Specific Identification'),
            ('FIFO', 'First In First Out')
        ],
        default='WAC',
        help_text="IFRS/SKK compliant costing method"
    )
    weighted_average_cost = models.DecimalField(
        max_digits=10, decimal_places=4, default=0,
        help_text="Current weighted average cost per unit"
    )
    total_cost_basis = models.DecimalField(
        max_digits=15, decimal_places=2, default=0,
        help_text="Total cost basis of current inventory"
    )
    currency = models.ForeignKey(
        Currency, on_delete=models.CASCADE,
        help_text="Currency for cost calculations"
    )
    last_cost_update = models.DateTimeField(auto_now=True)
    
    # NEW METHODS
    def get_current_unit_cost(self):
        """Get current unit cost based on costing method"""
        if self.costing_method == 'WAC':
            return self.weighted_average_cost
        elif self.costing_method == 'FIFO':
            return self._get_fifo_unit_cost()
        elif self.costing_method == 'SPECIFIC':
            return self._get_specific_unit_cost()
        return self.raw_material.price_per_unit  # Fallback
    
    def get_inventory_value(self):
        """Get total inventory value"""
        return self.quantity * self.get_current_unit_cost()
```

#### 1.2 Enhance RawMaterialInventoryHistory Model
```python
class RawMaterialInventoryHistory(models.Model):
    # Existing fields...
    raw_material_inventory = models.ForeignKey(RawMaterialInventory, on_delete=models.CASCADE)
    change_type = models.CharField(max_length=20, choices=RAW_MATERIAL_INVENTORY_CHANGE_TYPES)
    change_quantity = models.DecimalField(max_digits=10, decimal_places=2)
    purchase_order_item = models.ForeignKey('billing.PurchaseOrderItem', on_delete=models.SET_NULL, null=True, blank=True)
    case_item = models.ForeignKey('case.CaseItem', on_delete=models.SET_NULL, null=True, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # NEW COSTING FIELDS
    unit_cost = models.DecimalField(
        max_digits=10, decimal_places=4,
        help_text="Cost per unit for this transaction"
    )
    total_cost = models.DecimalField(
        max_digits=15, decimal_places=2,
        help_text="Total cost for this transaction"
    )
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE)
    
    # Weighted average cost after this transaction
    new_weighted_average_cost = models.DecimalField(
        max_digits=10, decimal_places=4, null=True, blank=True,
        help_text="New WAC after this transaction (for WAC method)"
    )
    
    # For FIFO/Specific identification
    batch_reference = models.CharField(
        max_length=100, blank=True,
        help_text="Batch reference for FIFO/Specific identification"
    )
```

#### 1.3 Create RawMaterialInventoryBatch Model (for FIFO/Specific)
```python
class RawMaterialInventoryBatch(models.Model):
    """Track individual batches for FIFO and Specific Identification methods"""
    inventory = models.ForeignKey(RawMaterialInventory, on_delete=models.CASCADE)
    batch_number = models.CharField(max_length=50, unique=True)
    purchase_order_item = models.ForeignKey('billing.PurchaseOrderItem', on_delete=models.CASCADE)
    
    quantity_purchased = models.DecimalField(max_digits=10, decimal_places=2)
    quantity_remaining = models.DecimalField(max_digits=10, decimal_places=2)
    unit_cost = models.DecimalField(max_digits=10, decimal_places=4)
    currency = models.ForeignKey(Currency, on_delete=models.CASCADE)
    
    purchase_date = models.DateTimeField()
    expiry_date = models.DateTimeField(null=True, blank=True)
    
    is_depleted = models.BooleanField(default=False)
    
    class Meta:
        ordering = ['purchase_date']  # For FIFO
```

### Phase 2: Inventory Costing Service

#### 2.1 Create InventoryCostingService
```python
# Create items/services/inventory_costing.py
from decimal import Decimal, ROUND_HALF_UP
from django.utils import timezone
from django.db import transaction
from items.models import RawMaterialInventory, RawMaterialInventoryHistory, RawMaterialInventoryBatch

class InventoryCostingService:
    """IFRS/SKK compliant inventory costing service"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @transaction.atomic
    def update_inventory_on_purchase(self, purchase_order_item):
        """Update inventory when materials are purchased"""
        try:
            inventory = self._get_or_create_inventory(purchase_order_item.raw_material)
            
            if inventory.costing_method == 'WAC':
                self._update_weighted_average_cost(inventory, purchase_order_item)
            elif inventory.costing_method == 'FIFO':
                self._create_fifo_batch(inventory, purchase_order_item)
            elif inventory.costing_method == 'SPECIFIC':
                self._create_specific_batch(inventory, purchase_order_item)
            
            # Create history record
            self._create_history_record(inventory, purchase_order_item, 'purchase')
            
        except Exception as e:
            self.logger.error(f"Error updating inventory on purchase: {str(e)}")
            raise
    
    def _update_weighted_average_cost(self, inventory, purchase_item):
        """Calculate and update weighted average cost"""
        # Current inventory value
        current_value = inventory.quantity * inventory.weighted_average_cost
        
        # New purchase value (convert currency if needed)
        new_unit_cost = self._convert_currency(
            purchase_item.price_per_unit,
            purchase_item.currency,
            inventory.currency
        )
        new_value = purchase_item.quantity * new_unit_cost
        
        # Calculate new weighted average
        total_value = current_value + new_value
        total_quantity = inventory.quantity + purchase_item.quantity
        
        if total_quantity > 0:
            new_wac = total_value / total_quantity
            inventory.weighted_average_cost = new_wac.quantize(Decimal('0.0001'), rounding=ROUND_HALF_UP)
            inventory.total_cost_basis = total_value
            inventory.quantity = total_quantity
            inventory.save()
    
    @transaction.atomic
    def update_inventory_on_consumption(self, case_item):
        """Update inventory when materials are consumed in production"""
        try:
            # Calculate material requirements for this case item
            material_requirements = self._calculate_case_item_materials(case_item)
            
            for material_req in material_requirements:
                inventory = RawMaterialInventory.objects.get(
                    raw_material=material_req['raw_material']
                )
                
                consumption_cost = self._calculate_consumption_cost(
                    inventory, material_req['quantity']
                )
                
                # Update inventory quantity and cost basis
                self._reduce_inventory(inventory, material_req['quantity'], consumption_cost)
                
                # Create history record
                self._create_consumption_history(inventory, case_item, material_req, consumption_cost)
                
        except Exception as e:
            self.logger.error(f"Error updating inventory on consumption: {str(e)}")
            raise
    
    def _calculate_consumption_cost(self, inventory, quantity_used):
        """Calculate cost of materials consumed based on costing method"""
        if inventory.costing_method == 'WAC':
            return quantity_used * inventory.weighted_average_cost
        elif inventory.costing_method == 'FIFO':
            return self._calculate_fifo_consumption_cost(inventory, quantity_used)
        elif inventory.costing_method == 'SPECIFIC':
            return self._calculate_specific_consumption_cost(inventory, quantity_used)
        
        # Fallback to base price
        return quantity_used * inventory.raw_material.price_per_unit
```

### Phase 3: Integration Points

#### 3.1 Update Purchase Order Signals
```python
# Modify items/signals.py
@receiver(post_save, sender=PurchaseOrderItem)
def update_raw_material_inventory_on_purchase(sender, instance, **kwargs):
    try:
        created = kwargs.get('created', False)
        if created:
            # Use the new costing service
            costing_service = InventoryCostingService()
            costing_service.update_inventory_on_purchase(instance)
        else:
            # Handle updates to existing purchase order items
            costing_service = InventoryCostingService()
            costing_service.handle_purchase_order_update(instance)
            
    except Exception as e:
        logger.exception("Error updating raw material inventory on purchase: %s", str(e))
```

#### 3.2 Update Item Cost Calculation
```python
# Modify items/models.py Item.cost() method
def cost(self):
    """Calculate item cost using actual inventory costs (IFRS/SKK compliant)"""
    total_cost = Decimal('0')
    raw_materials = self.itemrawmaterial_set.all()

    if not raw_materials.exists():
        return Decimal('0.00')

    costing_service = InventoryCostingService()
    
    for irm in raw_materials:
        # Use actual inventory cost instead of base price
        current_unit_cost = costing_service.get_current_material_cost(irm.raw_material)
        cost_in_raw_currency = current_unit_cost * irm.quantity
        
        # Apply currency conversion if needed
        if irm.raw_material.currency.code != self.currency.code:
            exchange_rate = ExchangeRate.get_exchange_rate(
                irm.raw_material.currency.code, self.currency.code
            )
            cost_in_item_currency = cost_in_raw_currency * exchange_rate
        else:
            cost_in_item_currency = cost_in_raw_currency
            
        total_cost += cost_in_item_currency
    
    return total_cost.quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
```

## 🚀 Implementation Timeline

### Week 1: Database Schema
- [ ] Create migration for new fields
- [ ] Add costing fields to RawMaterialInventory
- [ ] Add costing fields to RawMaterialInventoryHistory
- [ ] Create RawMaterialInventoryBatch model
- [ ] Test migrations

### Week 2: Core Service Development
- [ ] Implement InventoryCostingService
- [ ] Implement WAC calculations
- [ ] Implement FIFO batch tracking
- [ ] Implement Specific Identification
- [ ] Unit tests for costing logic

### Week 3: Integration
- [ ] Update purchase order signals
- [ ] Update Item.cost() method
- [ ] Add consumption tracking for CaseItems
- [ ] Integration tests

### Week 4: Data Migration & Validation
- [ ] Migrate existing inventory data
- [ ] Set initial costing methods
- [ ] Calculate initial WAC values
- [ ] Validate cost calculations

## 📋 Next Steps

1. **Review and approve this plan**
2. **Create database migrations**
3. **Implement core costing service**
4. **Update integration points**
5. **Test with sample data**
6. **Deploy to production**

This implementation will make the system 100% IFRS/SKK compliant while maintaining backward compatibility and improving cost accuracy significantly.
