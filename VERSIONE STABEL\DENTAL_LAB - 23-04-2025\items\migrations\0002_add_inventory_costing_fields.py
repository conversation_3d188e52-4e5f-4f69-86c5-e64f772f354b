# Generated migration for IFRS/SKK compliant inventory costing

from django.db import migrations, models
import django.db.models.deletion
from decimal import Decimal


class Migration(migrations.Migration):

    dependencies = [
        ('items', '0001_initial'),
    ]

    operations = [
        # Add costing fields to RawMaterialInventory
        migrations.AddField(
            model_name='rawmaterialinventory',
            name='costing_method',
            field=models.CharField(
                choices=[
                    ('WAC', 'Weighted Average Cost'),
                    ('SPECIFIC', 'Specific Identification'),
                    ('FIFO', 'First In First Out')
                ],
                default='WAC',
                help_text='IFRS/SKK compliant costing method',
                max_length=20
            ),
        ),
        migrations.AddField(
            model_name='rawmaterialinventory',
            name='weighted_average_cost',
            field=models.DecimalField(
                decimal_places=4,
                default=0,
                help_text='Current weighted average cost per unit',
                max_digits=10
            ),
        ),
        migrations.AddField(
            model_name='rawmaterialinventory',
            name='total_cost_basis',
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text='Total cost basis of current inventory',
                max_digits=15
            ),
        ),
        migrations.AddField(
            model_name='rawmaterialinventory',
            name='currency',
            field=models.ForeignKey(
                help_text='Currency for cost calculations',
                null=True,  # Allow null initially for data migration
                on_delete=django.db.models.deletion.CASCADE,
                to='items.currency'
            ),
        ),
        migrations.AddField(
            model_name='rawmaterialinventory',
            name='last_cost_update',
            field=models.DateTimeField(auto_now=True),
        ),
        
        # Add costing fields to RawMaterialInventoryHistory
        migrations.AddField(
            model_name='rawmaterialinventoryhistory',
            name='unit_cost',
            field=models.DecimalField(
                decimal_places=4,
                default=0,
                help_text='Cost per unit for this transaction',
                max_digits=10
            ),
        ),
        migrations.AddField(
            model_name='rawmaterialinventoryhistory',
            name='total_cost',
            field=models.DecimalField(
                decimal_places=2,
                default=0,
                help_text='Total cost for this transaction',
                max_digits=15
            ),
        ),
        migrations.AddField(
            model_name='rawmaterialinventoryhistory',
            name='currency',
            field=models.ForeignKey(
                null=True,  # Allow null initially for data migration
                on_delete=django.db.models.deletion.CASCADE,
                to='items.currency'
            ),
        ),
        migrations.AddField(
            model_name='rawmaterialinventoryhistory',
            name='new_weighted_average_cost',
            field=models.DecimalField(
                blank=True,
                decimal_places=4,
                help_text='New WAC after this transaction (for WAC method)',
                max_digits=10,
                null=True
            ),
        ),
        migrations.AddField(
            model_name='rawmaterialinventoryhistory',
            name='batch_reference',
            field=models.CharField(
                blank=True,
                help_text='Batch reference for FIFO/Specific identification',
                max_length=100
            ),
        ),
        
        # Create RawMaterialInventoryBatch model
        migrations.CreateModel(
            name='RawMaterialInventoryBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_number', models.CharField(max_length=50, unique=True)),
                ('quantity_purchased', models.DecimalField(decimal_places=2, max_digits=10)),
                ('quantity_remaining', models.DecimalField(decimal_places=2, max_digits=10)),
                ('unit_cost', models.DecimalField(decimal_places=4, max_digits=10)),
                ('purchase_date', models.DateTimeField()),
                ('expiry_date', models.DateTimeField(blank=True, null=True)),
                ('is_depleted', models.BooleanField(default=False)),
                ('currency', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='items.currency')),
                ('inventory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='items.rawmaterialinventory')),
                ('purchase_order_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='billing.purchaseorderitem')),
            ],
            options={
                'ordering': ['purchase_date'],
            },
        ),
    ]
