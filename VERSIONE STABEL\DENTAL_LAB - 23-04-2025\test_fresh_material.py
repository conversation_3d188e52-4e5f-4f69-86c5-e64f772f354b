#!/usr/bin/env python3

import os
import django
from decimal import Decimal

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

from items.models import RawMaterial, RawMaterialInventory, Currency, Unit
from billing.models import PurchaseOrder, PurchaseOrderItem, Supplier

def test_with_fresh_material():
    print("🧪 TESTING WITH FRESH MATERIAL (NO EXISTING INVENTORY)")
    print("=" * 60)

    # Create a new test material
    usd = Currency.objects.filter(code='USD').first()
    gram = Unit.objects.filter(name__icontains='gram').first()
    supplier = Supplier.objects.first()

    if not all([usd, gram, supplier]):
        print("❌ Missing test data")
        return False

    # Create a new test material
    test_material = RawMaterial.objects.create(
        name="Test Ceramic Powder",
        description="Test material for WAC calculation",
        unit=gram,  # Required field
        price_per_unit=Decimal('50.00'),  # Low price to ensure WAC method
        currency=usd,
        is_active=True
    )

    print(f"📦 Created test material: {test_material.name}")
    print(f"💰 Base price: ${test_material.price_per_unit}")

    # Verify no existing inventory
    existing_inventory = RawMaterialInventory.objects.filter(raw_material=test_material)
    print(f"📊 Existing inventory records: {existing_inventory.count()}")

    # Create purchase order
    po = PurchaseOrder.objects.create(
        supplier=supplier,
        order_date='2024-01-01',
        status='draft'
    )

    # Test scenario: Multiple purchases with different prices
    purchases = [
        {'qty': Decimal('100'), 'price': Decimal('75.00'), 'desc': 'First purchase'},
        {'qty': Decimal('150'), 'price': Decimal('80.00'), 'desc': 'Second purchase'},
        {'qty': Decimal('200'), 'price': Decimal('85.00'), 'desc': 'Third purchase'},
    ]

    print(f"\n📋 Purchase Scenario:")
    total_cost = Decimal('0')
    total_qty = Decimal('0')

    for i, purchase in enumerate(purchases, 1):
        cost = purchase['qty'] * purchase['price']
        total_cost += cost
        total_qty += purchase['qty']
        print(f"   {i}. {purchase['desc']}: {purchase['qty']} g @ ${purchase['price']}/g = ${cost}")

    expected_wac = total_cost / total_qty
    print(f"\n🎯 Expected Final WAC: ${expected_wac:.4f}")
    print(f"🎯 Expected Total Cost: ${total_cost}")
    print(f"🎯 Expected Total Quantity: {total_qty} g")

    # Execute purchases
    print(f"\n📥 Executing Purchases:")

    for i, purchase in enumerate(purchases, 1):
        print(f"\n   Purchase {i}: {purchase['desc']}")

        # Create purchase order item
        po_item = PurchaseOrderItem.objects.create(
            purchase_order=po,
            raw_material=test_material,
            quantity=purchase['qty'],
            unit=gram,
            price_per_unit=purchase['price'],
            currency=usd,
            description=purchase['desc']
        )

        # Check inventory after each purchase
        try:
            inventory = RawMaterialInventory.objects.get(
                raw_material=test_material,
                unit=gram
            )
            print(f"      📊 Quantity: {inventory.quantity} g")
            print(f"      💰 WAC: ${inventory.weighted_average_cost:.4f}")
            print(f"      💵 Total Value: ${inventory.total_cost_basis:.2f}")
            print(f"      🔧 Method: {inventory.costing_method}")

        except RawMaterialInventory.DoesNotExist:
            print(f"      ❌ No inventory record found")

    # Final verification
    print(f"\n✅ FINAL VERIFICATION:")
    try:
        final_inventory = RawMaterialInventory.objects.get(
            raw_material=test_material,
            unit=gram
        )

        print(f"   📊 Final Quantity: {final_inventory.quantity} g")
        print(f"   💰 Final WAC: ${final_inventory.weighted_average_cost:.4f}")
        print(f"   💵 Final Total Value: ${final_inventory.total_cost_basis:.2f}")

        # Check accuracy
        qty_match = abs(final_inventory.quantity - total_qty) < Decimal('0.01')
        wac_match = abs(final_inventory.weighted_average_cost - expected_wac) < Decimal('0.0001')
        value_match = abs(final_inventory.total_cost_basis - total_cost) < Decimal('0.01')

        print(f"\n🎯 ACCURACY CHECK:")
        print(f"   Quantity Match: {'✅' if qty_match else '❌'} ({final_inventory.quantity} vs {total_qty})")
        print(f"   WAC Match: {'✅' if wac_match else '❌'} (${final_inventory.weighted_average_cost:.4f} vs ${expected_wac:.4f})")
        print(f"   Value Match: {'✅' if value_match else '❌'} (${final_inventory.total_cost_basis:.2f} vs ${total_cost:.2f})")

        success = all([qty_match, wac_match, value_match])

        if success:
            print(f"\n🎉 SUCCESS: WAC calculation is 100% accurate!")
        else:
            print(f"\n❌ FAILURE: WAC calculation has discrepancies")

        # Clean up test data
        print(f"\n🧹 Cleaning up test data...")
        RawMaterialInventory.objects.filter(raw_material=test_material).delete()
        PurchaseOrderItem.objects.filter(raw_material=test_material).delete()
        test_material.delete()
        po.delete()
        print(f"✅ Test data cleaned up")

        return success

    except Exception as e:
        print(f"   ❌ Error in final verification: {str(e)}")
        return False

def test_item_cost_calculation():
    print(f"\n\n🧪 TESTING ITEM COST CALCULATION WITH IFRS/SKK COSTING")
    print("=" * 60)

    # Get a test item
    from items.models import Item
    items = Item.objects.all()

    if not items.exists():
        print("❌ No items found for testing")
        return False

    item = items.first()
    print(f"👑 Testing item: {item.name}")

    # Get cost breakdown
    try:
        cost_breakdown = item.cost_breakdown()
        total_cost = item.cost()

        print(f"\n💰 Total Cost: ${total_cost:.2f}")
        print(f"\n📋 Cost Breakdown:")

        for material in cost_breakdown:
            print(f"   • {material['material_name']}")
            print(f"     Quantity: {material['quantity']} {material['unit']}")
            print(f"     Unit Cost: ${material['unit_cost']:.4f}")
            print(f"     Total Cost: ${material['total_cost']:.2f}")
            print(f"     Method: {material['costing_method']}")
            print(f"     Currency: {material['currency']}")

        print(f"\n✅ Item cost calculation working with IFRS/SKK costing!")
        return True

    except Exception as e:
        print(f"❌ Error in item cost calculation: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE IFRS/SKK COSTING TEST")
    print("=" * 60)

    test1_success = test_with_fresh_material()
    test2_success = test_item_cost_calculation()

    print(f"\n\n🎯 FINAL RESULTS:")
    print(f"   WAC Calculation Test: {'✅ PASSED' if test1_success else '❌ FAILED'}")
    print(f"   Item Cost Test: {'✅ PASSED' if test2_success else '❌ FAILED'}")

    if test1_success and test2_success:
        print(f"\n🎉 ALL TESTS PASSED! IFRS/SKK Compliant Inventory Costing is working perfectly!")
    else:
        print(f"\n❌ Some tests failed. Please review the implementation.")
