#!/usr/bin/env python3

import os
import django
from decimal import Decimal

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

from items.models import RawMaterial, RawMaterialInventory, Currency, Unit
from billing.models import PurchaseOrder, PurchaseOrderItem, Supplier
from items.services.inventory_costing import InventoryCostingService

def debug_costing():
    print("🔍 DEBUGGING INVENTORY COSTING")
    print("=" * 40)

    # Check if we have test data
    materials = RawMaterial.objects.all()
    suppliers = Supplier.objects.all()
    currencies = Currency.objects.all()
    units = Unit.objects.all()

    print(f"📊 Available data:")
    print(f"   Materials: {materials.count()}")
    print(f"   Suppliers: {suppliers.count()}")
    print(f"   Currencies: {currencies.count()}")
    print(f"   Units: {units.count()}")

    print(f"\n📋 Material prices:")
    for mat in materials[:5]:
        print(f"   • {mat.name}: ${mat.price_per_unit}")

    if not all([materials.exists(), suppliers.exists(), currencies.exists(), units.exists()]):
        print("❌ Missing test data")
        return

    # Find a material with lower price for WAC testing
    material = None
    for mat in materials:
        if mat.price_per_unit < 100:  # Should use WAC method
            material = mat
            print(f"   Selected material for WAC testing: {mat.name} (${mat.price_per_unit})")
            break

    if not material:
        material = materials.first()
        print(f"   Using first material: {material.name} (${material.price_per_unit})")

    supplier = suppliers.first()
    currency = currencies.first()
    unit = units.first()

    print(f"\n🧪 Testing with:")
    print(f"   Material: {material.name}")
    print(f"   Supplier: {supplier.name}")
    print(f"   Currency: {currency.code}")
    print(f"   Unit: {unit.name}")

    # Check existing inventory
    existing_inventory = RawMaterialInventory.objects.filter(raw_material=material)
    print(f"\n📦 Existing inventory records: {existing_inventory.count()}")
    for inv in existing_inventory:
        print(f"   • {inv.raw_material.name} - {inv.quantity} {inv.unit.name}")
        print(f"     Method: {inv.costing_method}, WAC: {inv.weighted_average_cost}")

    # Create a simple purchase order
    try:
        po = PurchaseOrder.objects.create(
            supplier=supplier,
            order_date='2024-01-01',
            status='draft'
        )
        print(f"\n✅ Created PO: {po.id}")

        # Create purchase order item
        po_item = PurchaseOrderItem.objects.create(
            purchase_order=po,
            raw_material=material,
            quantity=Decimal('100'),
            unit=unit,
            price_per_unit=Decimal('50.00'),
            currency=currency
        )
        print(f"✅ Created PO Item: {po_item.id}")

        # Check inventory after purchase
        updated_inventory = RawMaterialInventory.objects.filter(raw_material=material)
        print(f"\n📦 Updated inventory records: {updated_inventory.count()}")
        for inv in updated_inventory:
            print(f"   • {inv.raw_material.name} - {inv.quantity} {inv.unit.name}")
            print(f"     Method: {inv.costing_method}, WAC: {inv.weighted_average_cost}")
            print(f"     Total Cost Basis: {inv.total_cost_basis}")

    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_costing()
