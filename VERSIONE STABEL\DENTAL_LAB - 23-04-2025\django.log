INFO 2025-05-24 18:04:25,853 autoreload 19352 8840 Watching for file changes with StatReloader
ERROR 2025-05-24 18:05:46,927 exceptions 8924 1500 Exception occurred: ['Invalid data']
NoneType: None
ERROR 2025-05-24 18:05:46,928 exceptions 8924 1500 Exception occurred: Unknown error
NoneType: None
ERROR 2025-05-24 18:05:47,052 validators 8924 1500 Validation error for case: '<' not supported between instances of 'str' and 'int'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\validators.py", line 382, in validate_model_data
    field_errors = CentralizedValidationService._validate_fields(model_class, data)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\validators.py", line 420, in _validate_fields
    validator(value)
    ~~~~~~~~~^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\validators.py", line 395, in __call__
    if self.compare(cleaned, limit_value):
       ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\validators.py", line 429, in compare
    return a < b
           ^^^^^
TypeError: '<' not supported between instances of 'str' and 'int'
ERROR 2025-05-24 18:06:48,572 exceptions 3996 1568 Exception occurred: ['Invalid data']
NoneType: None
ERROR 2025-05-24 18:06:48,573 exceptions 3996 1568 Exception occurred: Unknown error
NoneType: None
ERROR 2025-05-24 18:06:48,695 validators 3996 1568 Validation error for case: '__proxy__' object has no attribute 'extend'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\validators.py", line 411, in validate_model_data
    errors.setdefault(field, []).extend(field_error_list)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: '__proxy__' object has no attribute 'extend'
INFO 2025-05-24 18:10:18,073 basehttp 19352 20992 "GET / HTTP/1.1" 200 140233
INFO 2025-05-24 18:11:31,024 autoreload 19352 8840 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:11:32,415 autoreload 5728 11864 Watching for file changes with StatReloader
INFO 2025-05-24 18:11:35,217 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:37,843 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:40,222 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:42,260 basehttp 5728 20692 "GET / HTTP/1.1" 200 140233
INFO 2025-05-24 18:11:44,811 basehttp 5728 20692 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:11:49,311 basehttp 5728 20692 "GET /case/case/15/ HTTP/1.1" 200 166812
INFO 2025-05-24 18:11:55,852 autoreload 5728 11864 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:11:57,286 autoreload 21180 6188 Watching for file changes with StatReloader
INFO 2025-05-24 18:12:12,302 basehttp 21180 20996 "GET /case/case/15/update/ HTTP/1.1" 200 221456
INFO 2025-05-24 18:12:20,222 autoreload 21180 6188 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:12:21,765 autoreload 4020 5876 Watching for file changes with StatReloader
INFO 2025-05-24 18:12:27,931 basehttp 4020 19144 "GET /case/list/ HTTP/1.1" 200 138609
INFO 2025-05-24 18:12:29,845 basehttp 4020 19144 "GET /case/create/ HTTP/1.1" 200 222948
INFO 2025-05-24 18:12:42,809 autoreload 4020 5876 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:12:44,195 autoreload 21292 9656 Watching for file changes with StatReloader
INFO 2025-05-24 18:13:02,112 autoreload 21292 9656 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:13:03,638 autoreload 17420 3632 Watching for file changes with StatReloader
INFO 2025-05-24 18:13:48,176 basehttp 17420 5992 "POST /case/create/ HTTP/1.1" 302 0
INFO 2025-05-24 18:13:48,524 basehttp 17420 5992 "GET /case/case/16/ HTTP/1.1" 200 163413
INFO 2025-05-24 18:13:51,531 basehttp 17420 5992 "GET /case/list/ HTTP/1.1" 200 138335
ERROR 2025-05-24 18:16:34,629 status_synchronization 3192 22128 Error validating status consistency: No module named 'schedule'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\status_synchronization.py", line 320, in validate_status_consistency
    from schedule.models import Schedule
ModuleNotFoundError: No module named 'schedule'
ERROR 2025-05-24 18:16:34,635 dependency_management 3192 22128 Error getting task dependency chain: 'list' object has no attribute 'exclude'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\dependency_management.py", line 189, in get_task_dependency_chain
    incomplete_deps = direct_deps.exclude(status='completed')
                      ^^^^^^^^^^^^^^^^^^^
AttributeError: 'list' object has no attribute 'exclude'
ERROR 2025-05-24 18:16:34,641 exceptions 3192 22128 Exception occurred: Test error
NoneType: None
INFO 2025-05-24 18:20:42,907 autoreload 17420 3632 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\signals.py changed, reloading.
INFO 2025-05-24 18:20:44,745 autoreload 9624 21880 Watching for file changes with StatReloader
INFO 2025-05-24 18:20:47,848 basehttp 9624 7296 "GET /case/list/ HTTP/1.1" 200 138335
INFO 2025-05-24 18:20:50,055 basehttp 9624 7296 "GET /case/list/ HTTP/1.1" 200 138335
INFO 2025-05-24 18:20:54,905 basehttp 9624 7296 "GET /case/create/ HTTP/1.1" 200 232709
INFO 2025-05-24 18:21:24,082 basehttp 9624 7296 "GET /case/list/ HTTP/1.1" 200 138335
INFO 2025-05-24 18:21:26,521 basehttp 9624 7296 "GET /case/case/16/ HTTP/1.1" 302 0
INFO 2025-05-24 18:21:26,640 basehttp 9624 7296 "GET /case/list/ HTTP/1.1" 200 138698
INFO 2025-05-24 18:21:29,733 basehttp 9624 7296 "GET /case/case/16/ HTTP/1.1" 302 0
INFO 2025-05-24 18:21:29,847 basehttp 9624 7296 "GET /case/list/ HTTP/1.1" 200 138698
INFO 2025-05-24 18:22:43,476 autoreload 22444 16588 Watching for file changes with StatReloader
INFO 2025-05-24 18:22:47,133 basehttp 22444 11404 "GET /case/list/ HTTP/1.1" 200 138335
INFO 2025-05-24 18:22:49,785 basehttp 22444 11404 "GET /case/case/16/ HTTP/1.1" 200 194356
INFO 2025-05-24 18:23:14,983 basehttp 22444 11404 "GET /case/create/ HTTP/1.1" 200 232709
INFO 2025-05-24 18:23:55,936 basehttp 22444 11404 "GET /case/list/ HTTP/1.1" 200 138335
INFO 2025-05-24 18:23:58,050 autoreload 22444 16588 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\case\services.py changed, reloading.
INFO 2025-05-24 18:24:00,222 autoreload 20744 22220 Watching for file changes with StatReloader
INFO 2025-05-24 18:24:03,398 basehttp 20744 4928 "GET /case/gantt/ HTTP/1.1" 200 91688
INFO 2025-05-24 18:24:04,041 basehttp 20744 4928 "GET /static/js/production-timeline/utils.js HTTP/1.1" 200 5319
INFO 2025-05-24 18:24:04,070 basehttp 20744 15416 "GET /static/js/production-timeline/app.js HTTP/1.1" 200 5916
INFO 2025-05-24 18:24:04,124 basehttp 20744 15216 "GET /static/js/production-timeline/pdf-export.js HTTP/1.1" 200 8663
INFO 2025-05-24 18:24:04,131 basehttp 20744 8480 "GET /static/js/production-timeline/toast.js HTTP/1.1" 200 3997
INFO 2025-05-24 18:24:04,132 basehttp 20744 8904 "GET /static/js/production-timeline/case-management.js HTTP/1.1" 200 12228
INFO 2025-05-24 18:24:04,133 basehttp 20744 20708 "GET /static/js/production-timeline/gantt-chart-improved.js HTTP/1.1" 200 12348
INFO 2025-05-24 18:24:15,306 basehttp 20744 8904 "GET /case/case/calendar/ HTTP/1.1" 200 63594
INFO 2025-05-24 18:24:15,912 basehttp 20744 8904 "GET /case/case/calendar/ HTTP/1.1" 200 4244
INFO 2025-05-24 18:24:22,912 basehttp 20744 8904 "GET /case/dhtmlx-gantt/ HTTP/1.1" 200 90505
INFO 2025-05-24 18:24:31,597 basehttp 20744 8904 "GET / HTTP/1.1" 200 128546
INFO 2025-05-24 18:24:38,125 basehttp 20744 8904 "GET /?range=365 HTTP/1.1" 200 140244
ERROR 2025-05-24 18:26:01,687 scheduling_engine 6936 7584 Error analyzing department capacity: Field 'id' expected a number but got <__main__.test_capacity_planning_service.<locals>.MockDepartment object at 0x00000164654C8830>.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
TypeError: int() argument must be a string, a bytes-like object or a real number, not 'MockDepartment'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 624, in analyze_department_capacity
    items = ScheduleItem.objects.filter(
        department=department,
        start_time__date__gte=start_date,
        start_time__date__lte=end_date
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
TypeError: Field 'id' expected a number but got <__main__.test_capacity_planning_service.<locals>.MockDepartment object at 0x00000164654C8830>.
ERROR 2025-05-24 18:26:01,711 scheduling_engine 6936 7584 Error predicting capacity needs: Field 'id' expected a number but got <__main__.test_capacity_planning_service.<locals>.MockDepartment object at 0x00000164654C8830>.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\__init__.py", line 2128, in get_prep_value
    return int(value)
TypeError: int() argument must be a string, a bytes-like object or a real number, not 'MockDepartment'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 874, in predict_future_capacity_needs
    historical_items = ScheduleItem.objects.filter(
        department=department,
        start_time__date__gte=start_date,
        start_time__date__lte=end_date
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1585, in build_filter
    condition = self.build_lookup(lookups, col, value)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1412, in build_lookup
    lookup = lookup_class(lhs, rhs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\lookups.py", line 38, in __init__
    self.rhs = self.get_prep_lookup()
               ~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\related_lookups.py", line 112, in get_prep_lookup
    self.rhs = target_field.get_prep_value(self.rhs)
               ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\fields\__init__.py", line 2130, in get_prep_value
    raise e.__class__(
        "Field '%s' expected a number but got %r." % (self.name, value),
    ) from e
TypeError: Field 'id' expected a number but got <__main__.test_capacity_planning_service.<locals>.MockDepartment object at 0x00000164654C8830>.
INFO 2025-05-24 18:26:01,962 scheduling_engine 6936 7584 Starting advanced scheduling for case #TEST001
ERROR 2025-05-24 18:26:01,963 scheduling_engine 6936 7584 Scheduling failed for case #TEST001: min() iterable argument is empty
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 96, in schedule_case
    schedule = self._create_optimized_schedule(case, allocation)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 497, in _create_optimized_schedule
    start_time = min(alloc.start_time for alloc in allocations)
ValueError: min() iterable argument is empty
INFO 2025-05-24 18:26:01,964 scheduling_engine 6936 7584 Starting advanced scheduling for case #TEST001
ERROR 2025-05-24 18:26:01,964 scheduling_engine 6936 7584 Scheduling failed for case #TEST001: min() iterable argument is empty
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 96, in schedule_case
    schedule = self._create_optimized_schedule(case, allocation)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 497, in _create_optimized_schedule
    start_time = min(alloc.start_time for alloc in allocations)
ValueError: min() iterable argument is empty
INFO 2025-05-24 18:26:01,967 scheduling_engine 6936 7584 Starting advanced scheduling for case #TEST001
ERROR 2025-05-24 18:26:01,968 scheduling_engine 6936 7584 Scheduling failed for case #TEST001: min() iterable argument is empty
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 96, in schedule_case
    schedule = self._create_optimized_schedule(case, allocation)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 497, in _create_optimized_schedule
    start_time = min(alloc.start_time for alloc in allocations)
ValueError: min() iterable argument is empty
INFO 2025-05-24 18:26:01,971 scheduling_engine 6936 7584 Starting advanced scheduling for case #TEST001
ERROR 2025-05-24 18:26:01,971 scheduling_engine 6936 7584 Scheduling failed for case #TEST001: min() iterable argument is empty
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 96, in schedule_case
    schedule = self._create_optimized_schedule(case, allocation)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 497, in _create_optimized_schedule
    start_time = min(alloc.start_time for alloc in allocations)
ValueError: min() iterable argument is empty
INFO 2025-05-24 18:26:01,998 services 6936 7584 Conflict detection completed: 0 conflicts found
ERROR 2025-05-24 18:26:02,005 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "3D Printing": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "3D Printing": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,010 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "CAD/CAM Design": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "CAD/CAM Design": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,015 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Casting": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Casting": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,023 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Ceramics": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Ceramics": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,029 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Finishing": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Finishing": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,037 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Implants": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Implants": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,044 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Milling": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Milling": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,052 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Quality Control": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Quality Control": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,062 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Reception": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Reception": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,070 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Shipping": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Shipping": Must be "UserDepartment" instance.
ERROR 2025-05-24 18:26:02,077 scheduling_engine 6936 7584 Error analyzing department capacity: Cannot query "Test Department": Must be "UserDepartment" instance.
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 649, in analyze_department_capacity
    staff_count = User.objects.filter(departments=department, is_active=True).count()
                  ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1491, in filter
    return self._filter_or_exclude(False, args, kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1509, in _filter_or_exclude
    clone._filter_or_exclude_inplace(negate, args, kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\query.py", line 1516, in _filter_or_exclude_inplace
    self._query.add_q(Q(*args, **kwargs))
    ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1643, in add_q
    clause, _ = self._add_q(q_object, can_reuse)
                ~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1675, in _add_q
    child_clause, needed_inner = self.build_filter(
                                 ~~~~~~~~~~~~~~~~~^
        child,
        ^^^^^^
    ...<7 lines>...
        update_join_types=update_join_types,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1560, in build_filter
    self.check_related_objects(join_info.final_field, value, join_info.opts)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1369, in check_related_objects
    self.check_query_object_type(value, opts, field)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\db\models\sql\query.py", line 1346, in check_query_object_type
    raise ValueError(
    ...<2 lines>...
    )
ValueError: Cannot query "Test Department": Must be "UserDepartment" instance.
INFO 2025-05-24 18:26:02,082 services 6936 7584 Workload balance analysis completed
INFO 2025-05-24 18:26:15,035 scheduling_engine 14236 19472 Starting advanced scheduling for case #16
ERROR 2025-05-24 18:26:15,036 scheduling_engine 14236 19472 Scheduling failed for case #16: 'Case' object has no attribute 'id'
Traceback (most recent call last):
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 88, in schedule_case
    constraints = self._generate_constraints(case, requirements)
  File "C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\common\scheduling_engine.py", line 178, in _generate_constraints
    entity_id=case.id,
              ^^^^^^^
AttributeError: 'Case' object has no attribute 'id'
WARNING 2025-05-24 18:26:15,039 services 14236 19472 Failed to create optimized schedule for case #16: Scheduling failed: 'Case' object has no attribute 'id'
INFO 2025-05-24 18:27:09,661 basehttp 20744 8904 "GET /?range=30 HTTP/1.1" 200 128546
INFO 2025-05-24 18:27:25,333 basehttp 20744 8904 "GET /?range=180 HTTP/1.1" 200 140244
INFO 2025-05-24 18:27:34,605 basehttp 20744 8904 "GET /?range=365 HTTP/1.1" 200 140244
INFO 2025-05-24 18:27:48,425 basehttp 20744 8904 "GET / HTTP/1.1" 200 128546
INFO 2025-05-24 18:28:06,342 basehttp 20744 8904 "GET /?range=365 HTTP/1.1" 200 140244
INFO 2025-05-24 18:28:12,304 basehttp 20744 8904 "GET /case/case/15/ HTTP/1.1" 200 197940
INFO 2025-05-24 18:28:35,870 basehttp 20744 8904 "GET /case/case/15/update/ HTTP/1.1" 200 221456
INFO 2025-05-24 18:28:48,367 basehttp 20744 8904 "GET /case/list/ HTTP/1.1" 200 138335
INFO 2025-05-24 18:28:51,749 basehttp 20744 8904 "GET /case/case/16/ HTTP/1.1" 200 194356
INFO 2025-05-24 18:37:19,765 autoreload 20744 22220 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 18:37:21,274 autoreload 15936 20608 Watching for file changes with StatReloader
INFO 2025-05-24 18:38:04,289 autoreload 15936 20608 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 18:38:05,733 autoreload 6284 10380 Watching for file changes with StatReloader
INFO 2025-05-24 18:38:16,240 autoreload 6284 10380 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 18:38:17,444 autoreload 20668 21968 Watching for file changes with StatReloader
INFO 2025-05-24 18:38:38,153 autoreload 20668 21968 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\urls.py changed, reloading.
INFO 2025-05-24 18:38:39,372 autoreload 11788 3248 Watching for file changes with StatReloader
INFO 2025-05-24 18:42:41,346 basehttp 11788 22152 "GET /case/case/16/ HTTP/1.1" 200 194356
INFO 2025-05-24 18:43:41,406 basehttp 11788 22152 "GET /items/ HTTP/1.1" 200 94797
WARNING 2025-05-24 18:43:49,858 log 11788 22152 Not Found: /items/dashboard
WARNING 2025-05-24 18:43:49,859 basehttp 11788 22152 "GET /items/dashboard HTTP/1.1" 404 9746
INFO 2025-05-24 18:44:06,106 basehttp 11788 22152 "GET /items/mrp HTTP/1.1" 301 0
INFO 2025-05-24 18:44:06,549 basehttp 11788 21816 "GET /items/mrp/ HTTP/1.1" 302 0
INFO 2025-05-24 18:44:07,579 basehttp 11788 21816 "GET /items/ HTTP/1.1" 200 95149
INFO 2025-05-24 18:44:15,533 basehttp 11788 21816 "GET /items/mrp/ HTTP/1.1" 302 0
INFO 2025-05-24 18:44:16,433 basehttp 11788 21816 "GET /items/ HTTP/1.1" 200 95149
INFO 2025-05-24 18:48:34,489 basehttp 11788 21816 "GET /items/ HTTP/1.1" 200 94797
INFO 2025-05-24 18:48:49,438 basehttp 11788 21816 "GET /items/stock-alerts/ HTTP/1.1" 200 68364
INFO 2025-05-24 18:49:30,953 basehttp 11788 21816 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55361
INFO 2025-05-24 18:49:48,023 basehttp 11788 21816 "GET /items/purchase-recommendations/?days_ahead=90 HTTP/1.1" 200 55361
WARNING 2025-05-24 18:50:26,872 log 11788 21816 Not Found: /items/case-material-analysis
WARNING 2025-05-24 18:50:26,874 basehttp 11788 21816 "GET /items/case-material-analysis HTTP/1.1" 404 9785
INFO 2025-05-24 18:53:24,964 autoreload 9140 20908 Watching for file changes with StatReloader
INFO 2025-05-24 18:53:43,101 basehttp 9140 6784 "GET /items/mrp/ HTTP/1.1" 302 0
INFO 2025-05-24 18:53:43,532 basehttp 9140 6784 "GET /items/ HTTP/1.1" 200 95149
INFO 2025-05-24 18:53:47,718 basehttp 9140 6784 "GET /items/stock-alerts/ HTTP/1.1" 200 68364
INFO 2025-05-24 18:53:53,070 basehttp 9140 6784 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55361
INFO 2025-05-24 18:55:02,379 autoreload 628 20632 Watching for file changes with StatReloader
INFO 2025-05-24 18:55:14,714 basehttp 628 10592 "GET /items/ HTTP/1.1" 200 94797
INFO 2025-05-24 18:55:30,812 basehttp 628 3160 "GET /items/mrp/ HTTP/1.1" 200 54215
INFO 2025-05-24 18:55:44,600 basehttp 628 10592 "GET /items/mrp/ HTTP/1.1" 200 80730
ERROR 2025-05-24 18:55:57,323 exception 7456 21756 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-24 18:55:58,898 log 7456 21756 Bad Request: /items/mrp/
INFO 2025-05-24 18:56:17,968 autoreload 14176 3076 Watching for file changes with StatReloader
INFO 2025-05-24 18:56:29,243 basehttp 14176 5444 "GET /items/stock-alerts/ HTTP/1.1" 200 68364
INFO 2025-05-24 18:56:34,910 basehttp 14176 5444 "GET /items/mrp/ HTTP/1.1" 200 80730
INFO 2025-05-24 18:56:36,016 basehttp 14176 21576 "GET /items/mrp/ HTTP/1.1" 200 54215
INFO 2025-05-24 18:56:36,431 basehttp 14176 21104 "GET /items/stock-alerts/ HTTP/1.1" 200 41849
INFO 2025-05-24 18:56:36,988 basehttp 14176 356 "GET /items/purchase-recommendations/ HTTP/1.1" 200 28846
INFO 2025-05-24 23:25:43,341 autoreload 22608 20064 Watching for file changes with StatReloader
INFO 2025-05-24 23:25:52,708 basehttp 22608 22316 "GET / HTTP/1.1" 200 128546
INFO 2025-05-24 23:26:02,413 basehttp 22608 22316 "GET /?range=365 HTTP/1.1" 200 140244
INFO 2025-05-24 23:28:32,695 autoreload 7224 22720 Watching for file changes with StatReloader
INFO 2025-05-24 23:28:56,362 basehttp 22608 22316 "GET /?range=365 HTTP/1.1" 200 141954
INFO 2025-05-24 23:29:00,501 basehttp 22608 22316 "GET /items/mrp/ HTTP/1.1" 200 82440
INFO 2025-05-24 23:29:02,786 basehttp 22608 24852 "GET / HTTP/1.1" 302 0
INFO 2025-05-24 23:29:19,210 autoreload 20612 21824 Watching for file changes with StatReloader
INFO 2025-05-24 23:29:43,620 basehttp 22608 22316 "GET / HTTP/1.1" 200 130256
INFO 2025-05-24 23:29:46,768 basehttp 22608 22316 "GET /items/mrp/ HTTP/1.1" 200 82440
INFO 2025-05-24 23:29:52,890 basehttp 22608 23016 "GET /items/mrp/ HTTP/1.1" 200 54215
INFO 2025-05-24 23:29:53,223 basehttp 22608 19452 "GET /items/stock-alerts/ HTTP/1.1" 200 41849
INFO 2025-05-24 23:29:53,499 basehttp 22608 22316 "GET /items/stock-alerts/ HTTP/1.1" 200 70074
INFO 2025-05-24 23:29:53,658 basehttp 22608 3368 "GET /items/purchase-recommendations/ HTTP/1.1" 200 28846
INFO 2025-05-24 23:30:18,165 basehttp 22608 22316 "GET /items/purchase-recommendations/ HTTP/1.1" 200 57071
INFO 2025-05-24 23:30:29,413 basehttp 22608 22316 "POST /items/create-purchase-order/ HTTP/1.1" 302 0
INFO 2025-05-24 23:30:29,628 basehttp 22608 22316 "GET /items/purchase-recommendations/ HTTP/1.1" 200 57420
INFO 2025-05-24 23:32:29,438 autoreload 22608 20064 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:32:30,706 autoreload 21356 25736 Watching for file changes with StatReloader
INFO 2025-05-24 23:32:35,204 basehttp 21356 7244 "POST /items/create-purchase-order/ HTTP/1.1" 302 0
INFO 2025-05-24 23:32:35,379 basehttp 21356 7244 "GET /items/purchase-recommendations/ HTTP/1.1" 200 57420
INFO 2025-05-24 23:32:52,507 autoreload 21356 25736 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\services.py changed, reloading.
INFO 2025-05-24 23:32:53,424 autoreload 22112 21460 Watching for file changes with StatReloader
INFO 2025-05-24 23:33:04,815 autoreload 18256 25564 Watching for file changes with StatReloader
INFO 2025-05-24 23:33:23,275 basehttp 22112 3660 "GET /items/purchase-recommendations/ HTTP/1.1" 200 28846
INFO 2025-05-24 23:33:30,720 basehttp 22112 5836 "GET /items/purchase-recommendations/ HTTP/1.1" 200 57071
INFO 2025-05-24 23:33:41,540 basehttp 22112 5836 "POST /items/create-purchase-order/ HTTP/1.1" 302 0
INFO 2025-05-24 23:33:41,778 basehttp 22112 5836 "GET /items/purchase-recommendations/ HTTP/1.1" 200 57420
WARNING 2025-05-24 23:33:45,603 log 22112 5836 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-24 23:33:45,604 basehttp 22112 5836 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 3537
INFO 2025-05-24 23:34:31,247 autoreload 22112 21460 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:34:31,273 autoreload 18256 25564 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:34:32,114 autoreload 26084 7056 Watching for file changes with StatReloader
INFO 2025-05-24 23:34:32,133 autoreload 22096 25436 Watching for file changes with StatReloader
INFO 2025-05-24 23:35:32,980 autoreload 13328 7988 Watching for file changes with StatReloader
INFO 2025-05-24 23:35:41,074 basehttp 26084 23852 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55555
WARNING 2025-05-24 23:35:41,139 log 26084 23852 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-24 23:35:41,140 basehttp 26084 23852 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 3537
INFO 2025-05-24 23:35:47,066 basehttp 26084 23852 "GET /billing/purchase-orders/ HTTP/1.1" 200 73614
INFO 2025-05-24 23:35:52,705 basehttp 26084 23852 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55555
INFO 2025-05-24 23:36:00,245 basehttp 26084 23852 "POST /items/create-purchase-order/ HTTP/1.1" 302 0
INFO 2025-05-24 23:36:00,475 basehttp 26084 23852 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55904
INFO 2025-05-24 23:36:11,414 basehttp 26084 23852 "GET /billing/purchase-orders/ HTTP/1.1" 200 73614
INFO 2025-05-24 23:36:49,377 basehttp 26084 23852 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55555
INFO 2025-05-24 23:37:11,503 basehttp 26084 23852 "GET /items/purchase-recommendations/?days_ahead=30 HTTP/1.1" 200 55555
INFO 2025-05-24 23:37:14,961 basehttp 26084 23852 "GET /items/purchase-recommendations/?days_ahead=90 HTTP/1.1" 200 55555
INFO 2025-05-24 23:37:20,843 basehttp 26084 23852 "GET /items/mrp/ HTTP/1.1" 200 81492
INFO 2025-05-24 23:37:31,580 basehttp 26084 23852 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55555
INFO 2025-05-24 23:37:44,898 basehttp 26084 23852 "POST /items/create-purchase-order/ HTTP/1.1" 302 0
INFO 2025-05-24 23:37:45,090 basehttp 26084 23852 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55904
INFO 2025-05-24 23:39:16,608 basehttp 26084 23852 "GET /billing/purchase-orders/create/ HTTP/1.1" 200 66342
INFO 2025-05-24 23:40:50,903 autoreload 26084 7056 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:40:51,807 autoreload 22116 21124 Watching for file changes with StatReloader
INFO 2025-05-24 23:41:04,356 autoreload 22116 21124 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:41:05,149 autoreload 25200 23340 Watching for file changes with StatReloader
INFO 2025-05-24 23:41:33,030 autoreload 25200 23340 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:41:33,685 autoreload 23324 22004 Watching for file changes with StatReloader
INFO 2025-05-24 23:41:51,653 autoreload 23324 22004 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\billing\views.py changed, reloading.
INFO 2025-05-24 23:41:52,560 autoreload 11512 18720 Watching for file changes with StatReloader
INFO 2025-05-24 23:42:22,321 basehttp 11512 12252 "GET / HTTP/1.1" 200 130256
INFO 2025-05-24 23:42:30,310 autoreload 11512 18720 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\billing\views.py changed, reloading.
INFO 2025-05-24 23:42:31,471 autoreload 25504 23056 Watching for file changes with StatReloader
INFO 2025-05-24 23:43:43,489 autoreload 25504 23056 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:43:44,554 autoreload 11072 13288 Watching for file changes with StatReloader
INFO 2025-05-24 23:44:00,056 autoreload 11072 13288 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\billing\views.py changed, reloading.
INFO 2025-05-24 23:44:01,089 autoreload 9348 22160 Watching for file changes with StatReloader
INFO 2025-05-24 23:44:38,768 autoreload 7712 3216 Watching for file changes with StatReloader
INFO 2025-05-24 23:45:01,705 basehttp 9348 2076 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55559
INFO 2025-05-24 23:45:08,109 basehttp 9348 2076 "POST /items/create-purchase-order/ HTTP/1.1" 302 0
INFO 2025-05-24 23:45:08,292 basehttp 9348 2076 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55916
INFO 2025-05-24 23:47:04,173 autoreload 7712 3216 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:47:04,191 autoreload 9348 22160 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:47:05,110 autoreload 24428 23008 Watching for file changes with StatReloader
INFO 2025-05-24 23:47:05,123 autoreload 23960 25764 Watching for file changes with StatReloader
INFO 2025-05-24 23:47:20,459 autoreload 23236 3048 Watching for file changes with StatReloader
INFO 2025-05-24 23:47:31,615 basehttp 23960 25868 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55559
INFO 2025-05-24 23:47:45,438 basehttp 23960 25868 "GET /items/mrp/ HTTP/1.1" 200 81492
INFO 2025-05-24 23:49:06,666 autoreload 23960 25764 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:49:06,675 autoreload 23236 3048 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:49:07,616 autoreload 18728 7576 Watching for file changes with StatReloader
INFO 2025-05-24 23:49:07,621 autoreload 5424 10264 Watching for file changes with StatReloader
INFO 2025-05-24 23:49:30,140 autoreload 18728 7576 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:49:30,191 autoreload 5424 10264 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-24 23:49:31,077 autoreload 23296 21972 Watching for file changes with StatReloader
INFO 2025-05-24 23:49:31,141 autoreload 24620 23748 Watching for file changes with StatReloader
INFO 2025-05-24 23:49:37,410 basehttp 24620 22848 "GET /items/ HTTP/1.1" 200 96507
INFO 2025-05-24 23:49:53,820 basehttp 24620 22848 "GET /items/ HTTP/1.1" 200 96507
INFO 2025-05-24 23:49:56,682 basehttp 24620 22848 "GET /items/item/create/ HTTP/1.1" 200 88232
INFO 2025-05-24 23:50:03,758 basehttp 24620 23988 "GET /items/purchase-recommendations/ HTTP/1.1" 200 27334
ERROR 2025-05-24 23:51:11,611 exception 22356 22512 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-24 23:51:12,975 log 22356 22512 Bad Request: /items/purchase-recommendations/
INFO 2025-05-24 23:51:39,108 basehttp 24620 22848 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55559
INFO 2025-05-24 23:51:58,899 basehttp 24620 22848 "POST /items/create-purchase-order/ HTTP/1.1" 302 0
INFO 2025-05-24 23:51:59,045 basehttp 24620 22848 "GET /items/purchase-recommendations/ HTTP/1.1" 200 55916
INFO 2025-05-24 23:52:50,058 basehttp 24620 22848 "GET /items/ HTTP/1.1" 200 96507
INFO 2025-05-25 00:18:31,036 autoreload 24620 23748 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:18:31,146 autoreload 23296 21972 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:18:32,408 autoreload 15212 26008 Watching for file changes with StatReloader
INFO 2025-05-25 00:18:32,414 autoreload 16760 22284 Watching for file changes with StatReloader
INFO 2025-05-25 00:19:14,593 autoreload 16760 22284 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:19:14,599 autoreload 15212 26008 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:19:15,582 autoreload 25548 7048 Watching for file changes with StatReloader
INFO 2025-05-25 00:19:15,586 autoreload 18272 20664 Watching for file changes with StatReloader
INFO 2025-05-25 00:19:36,667 autoreload 18272 20664 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:19:36,827 autoreload 25548 7048 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:19:37,760 autoreload 26568 20168 Watching for file changes with StatReloader
INFO 2025-05-25 00:19:37,853 autoreload 5152 26168 Watching for file changes with StatReloader
INFO 2025-05-25 00:20:03,448 autoreload 5152 26168 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:20:03,546 autoreload 26568 20168 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:20:04,593 autoreload 21704 21884 Watching for file changes with StatReloader
INFO 2025-05-25 00:20:04,650 autoreload 19556 5424 Watching for file changes with StatReloader
INFO 2025-05-25 00:20:26,243 autoreload 21704 21884 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\signals.py changed, reloading.
INFO 2025-05-25 00:20:26,324 autoreload 19556 5424 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\signals.py changed, reloading.
INFO 2025-05-25 00:20:27,549 autoreload 23380 24124 Watching for file changes with StatReloader
INFO 2025-05-25 00:20:27,614 autoreload 10256 936 Watching for file changes with StatReloader
INFO 2025-05-25 00:22:41,649 autoreload 23380 24124 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\migrations\0003_populate_inventory_costing_data.py changed, reloading.
INFO 2025-05-25 00:22:41,682 autoreload 10256 936 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\migrations\0003_populate_inventory_costing_data.py changed, reloading.
INFO 2025-05-25 00:22:42,687 autoreload 6916 25876 Watching for file changes with StatReloader
INFO 2025-05-25 00:22:42,728 autoreload 16944 20456 Watching for file changes with StatReloader
INFO 2025-05-25 00:23:41,276 autoreload 14444 5396 Watching for file changes with StatReloader
INFO 2025-05-25 00:29:41,736 autoreload 22564 18336 Watching for file changes with StatReloader
INFO 2025-05-25 00:29:54,771 autoreload 11480 22620 Watching for file changes with StatReloader
INFO 2025-05-25 00:30:19,695 autoreload 25504 26516 Watching for file changes with StatReloader
INFO 2025-05-25 00:30:51,360 autoreload 14444 5396 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-25 00:30:51,361 autoreload 16944 20456 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-25 00:30:51,900 autoreload 25504 26516 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\views.py changed, reloading.
INFO 2025-05-25 00:30:52,488 autoreload 21772 8624 Watching for file changes with StatReloader
INFO 2025-05-25 00:30:52,533 autoreload 23480 23548 Watching for file changes with StatReloader
INFO 2025-05-25 00:30:52,833 autoreload 23984 22056 Watching for file changes with StatReloader
INFO 2025-05-25 00:31:11,133 autoreload 3760 21948 Watching for file changes with StatReloader
INFO 2025-05-25 00:31:27,124 basehttp 23480 18464 "GET /items/ HTTP/1.1" 200 96525
INFO 2025-05-25 00:33:04,325 autoreload 21772 8624 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:33:04,354 autoreload 23984 22056 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:33:04,411 autoreload 3760 21948 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:33:04,413 autoreload 23480 23548 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\items\models.py changed, reloading.
INFO 2025-05-25 00:33:05,624 autoreload 21904 26200 Watching for file changes with StatReloader
INFO 2025-05-25 00:33:05,655 autoreload 23136 25052 Watching for file changes with StatReloader
INFO 2025-05-25 00:33:05,736 autoreload 25864 21276 Watching for file changes with StatReloader
INFO 2025-05-25 00:33:05,770 autoreload 23792 23296 Watching for file changes with StatReloader
INFO 2025-05-25 00:33:30,194 basehttp 23136 4020 "GET /items/ HTTP/1.1" 200 96525
INFO 2025-05-25 00:33:32,423 basehttp 23136 4020 "GET /items/ HTTP/1.1" 200 96525
INFO 2025-05-25 00:33:33,076 basehttp 23136 4020 "GET / HTTP/1.1" 200 130256
INFO 2025-05-25 00:33:36,085 basehttp 23136 4020 "GET /case/list/ HTTP/1.1" 200 140045
INFO 2025-05-25 00:33:56,329 basehttp 23136 4020 "GET /case/case/13/update/ HTTP/1.1" 200 226454
INFO 2025-05-25 00:34:18,530 basehttp 23136 4020 "POST /case/case/13/update/ HTTP/1.1" 200 220432
INFO 2025-05-25 00:34:42,994 basehttp 23136 4020 "POST /case/case/13/update/ HTTP/1.1" 302 0
INFO 2025-05-25 00:34:43,090 basehttp 23136 4020 "GET /case/case/13/ HTTP/1.1" 200 211494
INFO 2025-05-25 00:35:09,481 basehttp 23136 4020 "GET / HTTP/1.1" 200 130256
INFO 2025-05-25 00:35:11,907 basehttp 23136 4020 "GET /case/list/ HTTP/1.1" 200 139696
INFO 2025-05-25 00:36:01,264 basehttp 23136 4020 "GET /case/case/11/ HTTP/1.1" 200 193690
INFO 2025-05-25 00:36:05,982 views 23136 4020 Found case with case_number=11
INFO 2025-05-25 00:36:05,992 views 23136 4020 Found 0 case items for Case 11
INFO 2025-05-25 00:36:05,997 views 23136 4020 Number of items in initial_items_data: 0
INFO 2025-05-25 00:36:06,002 views 23136 4020 Formset has 3 forms
INFO 2025-05-25 00:36:06,003 views 23136 4020 Form 0: initial={}
INFO 2025-05-25 00:36:06,004 views 23136 4020 Form 1: initial={}
INFO 2025-05-25 00:36:06,005 views 23136 4020 Form 2: initial={}
INFO 2025-05-25 00:36:06,093 basehttp 23136 4020 "GET /billing/invoices/create/from-case/11/ HTTP/1.1" 200 78014
INFO 2025-05-25 00:36:44,220 basehttp 23136 4020 "GET /billing/api/item-details/11/ HTTP/1.1" 200 146
INFO 2025-05-25 00:36:56,728 basehttp 23136 4020 "GET /case/list/ HTTP/1.1" 200 139696
INFO 2025-05-25 00:37:07,561 basehttp 23136 4020 "GET /case/case/16/ HTTP/1.1" 200 196077
INFO 2025-05-25 00:37:19,315 views 23136 4020 Found case with case_number=16
INFO 2025-05-25 00:37:19,328 views 23136 4020 Found 1 case items for Case 16
INFO 2025-05-25 00:37:19,333 views 23136 4020 Case item 1: ID=15, Item=Metal Framework, Quantity=1
INFO 2025-05-25 00:37:19,334 views 23136 4020 Adding case item to initial_data: Metal Framework, quantity: 1
INFO 2025-05-25 00:37:19,335 views 23136 4020 Added item data: {'item': <Item: Metal Framework>, 'description': 'Metal Framework', 'quantity': 1, 'selling_price': Decimal('400.00'), 'currency': <Currency: USD>}
INFO 2025-05-25 00:37:19,336 views 23136 4020 Number of items in initial_items_data: 1
INFO 2025-05-25 00:37:19,342 views 23136 4020 Formset has 3 forms
INFO 2025-05-25 00:37:19,343 views 23136 4020 Form 0: initial={'item': <Item: Metal Framework>, 'description': 'Metal Framework', 'quantity': 1, 'selling_price': Decimal('400.00'), 'currency': <Currency: USD>}
INFO 2025-05-25 00:37:19,346 views 23136 4020 Form 1: initial={}
INFO 2025-05-25 00:37:19,347 views 23136 4020 Form 2: initial={}
INFO 2025-05-25 00:37:19,429 basehttp 23136 4020 "GET /billing/invoices/create/from-case/16/ HTTP/1.1" 200 78077
INFO 2025-05-25 00:37:27,712 basehttp 23136 4020 "GET / HTTP/1.1" 200 130256
INFO 2025-05-25 00:37:31,638 basehttp 23136 4020 "GET /case/list/ HTTP/1.1" 200 139696
INFO 2025-05-25 00:37:57,918 basehttp 23136 4020 "GET /billing/invoices/ HTTP/1.1" 200 72584
INFO 2025-05-25 00:38:30,768 basehttp 23136 4020 "GET /billing/invoices/ HTTP/1.1" 200 72584
INFO 2025-05-25 00:38:34,374 basehttp 23136 4020 "GET /billing/invoices/select-case/ HTTP/1.1" 200 63386
INFO 2025-05-25 00:38:54,207 basehttp 23136 4020 "GET /case/list/ HTTP/1.1" 200 139696
INFO 2025-05-25 00:39:10,422 basehttp 23136 4020 "GET /case/list/?page=2 HTTP/1.1" 200 116919
INFO 2025-05-25 00:39:27,154 basehttp 23136 4020 "GET /billing/api/case-details/16/ HTTP/1.1" 200 499
INFO 2025-05-25 00:39:33,876 basehttp 23136 4020 "GET /case/list/ HTTP/1.1" 200 139696
INFO 2025-05-25 00:39:37,926 basehttp 23136 4020 "GET /case/case/16/ HTTP/1.1" 200 196077
INFO 2025-05-25 00:39:40,869 views 23136 4020 Found case with case_number=16
INFO 2025-05-25 00:39:40,883 views 23136 4020 Found 1 case items for Case 16
INFO 2025-05-25 00:39:40,888 views 23136 4020 Case item 1: ID=15, Item=Metal Framework, Quantity=1
INFO 2025-05-25 00:39:40,888 views 23136 4020 Adding case item to initial_data: Metal Framework, quantity: 1
INFO 2025-05-25 00:39:40,889 views 23136 4020 Added item data: {'item': <Item: Metal Framework>, 'description': 'Metal Framework', 'quantity': 1, 'selling_price': Decimal('400.00'), 'currency': <Currency: USD>}
INFO 2025-05-25 00:39:40,889 views 23136 4020 Number of items in initial_items_data: 1
INFO 2025-05-25 00:39:40,897 views 23136 4020 Formset has 3 forms
INFO 2025-05-25 00:39:40,898 views 23136 4020 Form 0: initial={'item': <Item: Metal Framework>, 'description': 'Metal Framework', 'quantity': 1, 'selling_price': Decimal('400.00'), 'currency': <Currency: USD>}
INFO 2025-05-25 00:39:40,899 views 23136 4020 Form 1: initial={}
INFO 2025-05-25 00:39:40,899 views 23136 4020 Form 2: initial={}
INFO 2025-05-25 00:39:40,984 basehttp 23136 4020 "GET /billing/invoices/create/from-case/16/ HTTP/1.1" 200 78077
INFO 2025-05-25 00:39:58,319 basehttp 23136 4020 "GET /items/ HTTP/1.1" 200 96525
INFO 2025-05-25 00:40:57,015 basehttp 23792 5408 "GET /scheduling/report/ HTTP/1.1" 200 49739
INFO 2025-05-25 00:41:10,995 basehttp 23792 5408 "GET /scheduling/schedules/ HTTP/1.1" 200 56779
INFO 2025-05-25 00:41:16,069 basehttp 23792 5408 "GET /scheduling/schedules/select-case/ HTTP/1.1" 200 62517
INFO 2025-05-25 00:41:24,123 basehttp 23792 5408 "GET /scheduling/schedules/create/16/ HTTP/1.1" 200 67060
INFO 2025-05-25 00:41:38,305 basehttp 23792 5408 "GET /scheduling/schedules/ HTTP/1.1" 200 56779
INFO 2025-05-25 00:41:43,826 basehttp 23792 5408 "GET /scheduling/schedules/2/ HTTP/1.1" 200 79077
INFO 2025-05-25 00:41:57,608 basehttp 23792 5408 "GET /reports/ HTTP/1.1" 200 56654
INFO 2025-05-25 00:41:59,151 basehttp 23792 5408 "GET /reports/reports/case-volume/ HTTP/1.1" 200 59087
INFO 2025-05-25 00:41:59,382 basehttp 23792 5408 "GET /static/css/reports.css HTTP/1.1" 200 4594
INFO 2025-05-25 00:41:59,386 basehttp 23792 25868 "GET /static/js/reports.js HTTP/1.1" 200 10174
INFO 2025-05-25 00:49:19,887 basehttp 23792 24788 "GET /reports/ HTTP/1.1" 200 56654
INFO 2025-05-25 00:49:35,347 autoreload 21904 26200 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 00:49:35,391 autoreload 23792 23296 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 00:49:36,619 autoreload 21772 8448 Watching for file changes with StatReloader
INFO 2025-05-25 00:49:36,666 autoreload 21392 3616 Watching for file changes with StatReloader
INFO 2025-05-25 00:50:12,832 autoreload 21392 3616 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 00:50:12,865 autoreload 21772 8448 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 00:50:13,802 autoreload 18380 25996 Watching for file changes with StatReloader
INFO 2025-05-25 00:50:13,821 autoreload 1152 16248 Watching for file changes with StatReloader
INFO 2025-05-25 00:50:59,238 autoreload 1152 16248 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 00:50:59,264 autoreload 18380 25996 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 00:51:00,165 autoreload 22296 23092 Watching for file changes with StatReloader
INFO 2025-05-25 00:51:00,217 autoreload 17440 11520 Watching for file changes with StatReloader
INFO 2025-05-25 00:51:52,659 autoreload 22296 23092 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 00:51:52,662 autoreload 17440 11520 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 00:51:53,496 autoreload 6188 13600 Watching for file changes with StatReloader
INFO 2025-05-25 00:51:53,537 autoreload 5216 15940 Watching for file changes with StatReloader
INFO 2025-05-25 00:52:27,643 autoreload 5216 15940 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 00:52:28,484 autoreload 23908 848 Watching for file changes with StatReloader
INFO 2025-05-25 00:52:28,686 autoreload 6188 13600 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 00:52:29,514 autoreload 5792 11504 Watching for file changes with StatReloader
INFO 2025-05-25 00:53:17,171 autoreload 5792 11504 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\urls.py changed, reloading.
INFO 2025-05-25 00:53:17,172 autoreload 23908 848 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\urls.py changed, reloading.
INFO 2025-05-25 00:53:18,122 autoreload 5968 17680 Watching for file changes with StatReloader
INFO 2025-05-25 00:53:18,149 autoreload 22728 15532 Watching for file changes with StatReloader
INFO 2025-05-25 00:55:38,081 basehttp 22728 22696 "GET /reports/reports/item_usage_report/ HTTP/1.1" 200 924
INFO 2025-05-25 00:55:44,982 basehttp 22728 22696 "GET /reports/reports/dentist-leaderboard/ HTTP/1.1" 200 926
INFO 2025-05-25 00:55:50,490 basehttp 22728 22696 "GET /reports/reports/dentist-financials/ HTTP/1.1" 200 931
INFO 2025-05-25 00:55:57,974 basehttp 22728 22696 "GET /reports/reports/case-progress/ HTTP/1.1" 200 927
INFO 2025-05-25 00:56:03,059 basehttp 22728 22696 "GET /reports/financial-report/ HTTP/1.1" 200 923
INFO 2025-05-25 00:56:11,046 basehttp 22728 22696 "GET /reports/reports/dentist-performance/ HTTP/1.1" 200 947
INFO 2025-05-25 00:56:25,459 basehttp 22728 22696 "GET /reports/reports/case-status/ HTTP/1.1" 200 925
INFO 2025-05-25 00:57:39,217 basehttp 22728 22696 "GET /reports/reports/case-status/ HTTP/1.1" 200 925
WARNING 2025-05-25 00:57:42,490 log 22728 22696 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-25 00:57:42,490 basehttp 22728 22696 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 4151
INFO 2025-05-25 01:01:54,105 autoreload 5968 17680 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:01:54,123 autoreload 22728 15532 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:01:55,099 autoreload 4744 21424 Watching for file changes with StatReloader
INFO 2025-05-25 01:01:55,144 autoreload 15488 23812 Watching for file changes with StatReloader
WARNING 2025-05-25 01:17:10,222 log 15488 26088 Not Found: /reports/profitability-analysis
WARNING 2025-05-25 01:17:10,224 basehttp 15488 26088 "GET /reports/profitability-analysis HTTP/1.1" 404 11584
WARNING 2025-05-25 01:17:10,270 log 15488 26088 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-25 01:17:10,270 basehttp 15488 26088 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 4151
INFO 2025-05-25 01:17:33,237 basehttp 15488 26088 "GET /reports/reports/profitability-analysis HTTP/1.1" 301 0
INFO 2025-05-25 01:17:33,590 basehttp 15488 25064 "GET /reports/reports/profitability-analysis/ HTTP/1.1" 200 936
WARNING 2025-05-25 01:17:33,622 log 15488 25064 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-25 01:17:33,623 basehttp 15488 25064 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 4151
INFO 2025-05-25 01:19:17,197 autoreload 15488 23812 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\urls.py changed, reloading.
INFO 2025-05-25 01:19:17,603 autoreload 4744 21424 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\urls.py changed, reloading.
INFO 2025-05-25 01:19:18,186 autoreload 17080 23704 Watching for file changes with StatReloader
INFO 2025-05-25 01:19:18,560 autoreload 10532 26132 Watching for file changes with StatReloader
WARNING 2025-05-25 01:19:27,523 log 17080 3460 Not Found: /reports/reports/profitability-analysis/
WARNING 2025-05-25 01:19:27,525 basehttp 17080 3460 "GET /reports/reports/profitability-analysis/ HTTP/1.1" 404 11441
WARNING 2025-05-25 01:19:27,556 log 17080 3460 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-25 01:19:27,557 basehttp 17080 3460 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 4151
INFO 2025-05-25 01:19:38,398 basehttp 17080 3460 "GET /reports/profitability-analysis/ HTTP/1.1" 200 936
WARNING 2025-05-25 01:19:38,441 log 17080 3460 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-25 01:19:38,442 basehttp 17080 3460 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 4151
INFO 2025-05-25 01:20:11,639 autoreload 19648 15068 Watching for file changes with StatReloader
INFO 2025-05-25 01:20:23,032 basehttp 17080 3460 "GET /reports/profitability-analysis/ HTTP/1.1" 200 936
WARNING 2025-05-25 01:20:23,053 log 17080 3460 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-25 01:20:23,054 basehttp 17080 3460 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 4151
INFO 2025-05-25 01:20:26,919 basehttp 17080 3460 "GET /reports HTTP/1.1" 301 0
INFO 2025-05-25 01:20:26,961 basehttp 17080 11324 "GET /reports/ HTTP/1.1" 200 922
WARNING 2025-05-25 01:20:27,006 log 17080 11324 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-25 01:20:27,008 basehttp 17080 11324 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 4151
INFO 2025-05-25 01:20:31,276 basehttp 17080 11324 "GET /reports/ HTTP/1.1" 200 922
WARNING 2025-05-25 01:20:31,321 log 17080 11324 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-25 01:20:31,323 basehttp 17080 11324 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 4151
INFO 2025-05-25 01:20:34,253 basehttp 17080 11324 "GET /reports/ HTTP/1.1" 200 922
WARNING 2025-05-25 01:20:34,280 log 17080 11324 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-25 01:20:34,281 basehttp 17080 11324 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 4151
ERROR 2025-05-25 01:22:33,877 exception 24408 25868 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:22:33,898 log 24408 25868 Bad Request: /reports/
ERROR 2025-05-25 01:22:33,899 exception 24408 25868 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:22:33,915 log 24408 25868 Bad Request: /reports/profitability-analysis/
ERROR 2025-05-25 01:22:33,916 exception 24408 25868 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:22:33,931 log 24408 25868 Bad Request: /reports/cash-flow-forecast/
ERROR 2025-05-25 01:22:33,932 exception 24408 25868 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:22:33,947 log 24408 25868 Bad Request: /reports/quality-metrics/
ERROR 2025-05-25 01:22:33,949 exception 24408 25868 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:22:33,965 log 24408 25868 Bad Request: /reports/workflow-efficiency/
ERROR 2025-05-25 01:22:33,966 exception 24408 25868 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:22:33,981 log 24408 25868 Bad Request: /reports/inventory-optimization/
ERROR 2025-05-25 01:22:33,982 exception 24408 25868 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:22:33,997 log 24408 25868 Bad Request: /reports/demand-forecasting/
ERROR 2025-05-25 01:22:33,998 exception 24408 25868 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:22:34,013 log 24408 25868 Bad Request: /reports/customer-relationship/
INFO 2025-05-25 01:23:15,677 basehttp 17080 11324 "GET /reports/ HTTP/1.1" 200 922
WARNING 2025-05-25 01:23:15,712 log 17080 11324 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-25 01:23:15,713 basehttp 17080 11324 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 4151
WARNING 2025-05-25 01:23:32,239 log 17080 11324 Not Found: /reports/reports_dashboard
WARNING 2025-05-25 01:23:32,240 basehttp 17080 11324 "GET /reports/reports_dashboard HTTP/1.1" 404 11399
WARNING 2025-05-25 01:23:32,281 log 17080 11324 Not Found: /.well-known/appspecific/com.chrome.devtools.json
WARNING 2025-05-25 01:23:32,282 basehttp 17080 11324 "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 4151
INFO 2025-05-25 01:23:57,489 basehttp 17080 22428 "GET /reports/cycletimes/ HTTP/1.1" 200 924
ERROR 2025-05-25 01:25:49,090 exception 19376 17960 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:25:49,110 log 19376 17960 Bad Request: /reports/
ERROR 2025-05-25 01:25:49,111 exception 19376 17960 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:25:49,126 log 19376 17960 Bad Request: /reports/profitability-analysis/
ERROR 2025-05-25 01:25:49,127 exception 19376 17960 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:25:49,141 log 19376 17960 Bad Request: /reports/cash-flow-forecast/
ERROR 2025-05-25 01:25:49,143 exception 19376 17960 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:25:49,157 log 19376 17960 Bad Request: /reports/quality-metrics/
ERROR 2025-05-25 01:25:49,158 exception 19376 17960 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:25:49,173 log 19376 17960 Bad Request: /reports/workflow-efficiency/
ERROR 2025-05-25 01:25:49,174 exception 19376 17960 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:25:49,190 log 19376 17960 Bad Request: /reports/inventory-optimization/
ERROR 2025-05-25 01:25:49,192 exception 19376 17960 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:25:49,206 log 19376 17960 Bad Request: /reports/demand-forecasting/
ERROR 2025-05-25 01:25:49,208 exception 19376 17960 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 119, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 186, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-05-25 01:25:49,222 log 19376 17960 Bad Request: /reports/customer-relationship/
INFO 2025-05-25 01:26:05,844 autoreload 17080 23704 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\LAB\settings.py changed, reloading.
INFO 2025-05-25 01:26:06,667 autoreload 16288 12460 Watching for file changes with StatReloader
INFO 2025-05-25 01:26:06,923 autoreload 10532 26132 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\LAB\settings.py changed, reloading.
INFO 2025-05-25 01:26:07,723 autoreload 21196 14932 Watching for file changes with StatReloader
INFO 2025-05-25 01:26:24,277 autoreload 26060 15220 Watching for file changes with StatReloader
INFO 2025-05-25 01:26:31,566 basehttp 16288 14788 "GET /reports/cycletimes/ HTTP/1.1" 200 924
WARNING 2025-05-25 01:29:07,325 log 16288 14788 Not Found: /reports/profitability_analysis_report/
WARNING 2025-05-25 01:29:07,327 basehttp 16288 14788 "GET /reports/profitability_analysis_report/ HTTP/1.1" 404 11438
WARNING 2025-05-25 01:29:28,024 log 16288 14788 Not Found: /reports/profitability_analysis_report
WARNING 2025-05-25 01:29:28,026 basehttp 16288 14788 "GET /reports/profitability_analysis_report HTTP/1.1" 404 11435
WARNING 2025-05-25 01:29:44,189 log 16288 14788 Not Found: /reports/profitability_analysis
WARNING 2025-05-25 01:29:44,190 basehttp 16288 14788 "GET /reports/profitability_analysis HTTP/1.1" 404 11414
INFO 2025-05-25 01:29:56,965 basehttp 16288 14788 "GET /reports/profitability-analysis HTTP/1.1" 301 0
INFO 2025-05-25 01:29:57,287 basehttp 16288 2056 "GET /reports/profitability-analysis/ HTTP/1.1" 200 936
INFO 2025-05-25 01:31:37,243 autoreload 21196 14932 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:31:37,859 autoreload 16288 12460 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:31:38,126 autoreload 23040 24948 Watching for file changes with StatReloader
INFO 2025-05-25 01:31:38,743 autoreload 20876 20076 Watching for file changes with StatReloader
INFO 2025-05-25 01:31:43,286 basehttp 23040 26112 "GET /reports/ HTTP/1.1" 200 64949
INFO 2025-05-25 01:31:50,611 autoreload 23040 24948 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:31:50,616 autoreload 20876 20076 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:31:51,769 autoreload 7884 8588 Watching for file changes with StatReloader
INFO 2025-05-25 01:31:51,774 autoreload 17992 3320 Watching for file changes with StatReloader
INFO 2025-05-25 01:32:14,547 basehttp 17992 20816 "GET /reports/?start_date=2025-01-01&end_date=2025-05-25 HTTP/1.1" 200 66655
INFO 2025-05-25 01:32:15,849 autoreload 17992 3320 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:32:16,087 autoreload 7884 8588 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:32:17,300 autoreload 23876 22532 Watching for file changes with StatReloader
INFO 2025-05-25 01:32:17,338 autoreload 26060 6560 Watching for file changes with StatReloader
INFO 2025-05-25 01:32:29,072 autoreload 23876 22532 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:32:29,131 autoreload 26060 6560 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:32:30,312 autoreload 18808 24728 Watching for file changes with StatReloader
INFO 2025-05-25 01:32:30,325 autoreload 22420 25888 Watching for file changes with StatReloader
INFO 2025-05-25 01:32:41,931 autoreload 18808 24728 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:32:42,035 autoreload 22420 25888 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:32:43,157 autoreload 2136 10192 Watching for file changes with StatReloader
INFO 2025-05-25 01:32:43,205 autoreload 16064 22996 Watching for file changes with StatReloader
INFO 2025-05-25 01:32:55,521 basehttp 2136 25904 "GET /reports/ HTTP/1.1" 200 64949
INFO 2025-05-25 01:33:04,774 basehttp 2136 25904 "GET /reports/profitability-analysis/ HTTP/1.1" 200 72929
INFO 2025-05-25 01:34:01,142 basehttp 2136 25904 "GET /reports/ HTTP/1.1" 200 64949
INFO 2025-05-25 01:34:05,209 basehttp 2136 25904 "GET /reports/quality-metrics/ HTTP/1.1" 200 73419
INFO 2025-05-25 01:34:31,844 basehttp 2136 25904 "GET /reports/ HTTP/1.1" 200 64949
INFO 2025-05-25 01:34:37,113 basehttp 2136 25904 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:35:44,337 basehttp 2136 25904 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:35:46,003 basehttp 2136 25904 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:35:49,075 basehttp 2136 25904 "GET /reports/workflow-efficiency/ HTTP/1.1" 200 935
INFO 2025-05-25 01:35:52,185 basehttp 2136 25904 "GET /reports/demand-forecasting/ HTTP/1.1" 200 932
INFO 2025-05-25 01:35:55,601 basehttp 2136 25904 "GET /reports/customer-relationship/ HTTP/1.1" 200 937
INFO 2025-05-25 01:36:25,605 basehttp 2136 25904 "GET /reports/?start_date=2024-06-25&end_date=2025-05-25 HTTP/1.1" 200 66655
INFO 2025-05-25 01:36:57,888 basehttp 2136 25904 "GET /reports/workflow-efficiency/ HTTP/1.1" 200 935
INFO 2025-05-25 01:37:00,375 basehttp 2136 25904 "GET /reports/customer-relationship/ HTTP/1.1" 200 937
INFO 2025-05-25 01:37:06,672 basehttp 2136 25904 "GET /reports/case-volume/ HTTP/1.1" 200 58994
INFO 2025-05-25 01:37:11,241 basehttp 2136 25904 "GET /reports/case-progress/ HTTP/1.1" 200 50411
INFO 2025-05-25 01:37:15,689 basehttp 2136 25904 "GET /reports/revenue/ HTTP/1.1" 200 53971
INFO 2025-05-25 01:37:21,205 basehttp 2136 25904 "GET /reports/dentist-financials/ HTTP/1.1" 200 62055
INFO 2025-05-25 01:37:27,643 basehttp 2136 25904 "GET /reports/dentist-financials/?start_date=2024-04-25&end_date=2025-05-25 HTTP/1.1" 200 67508
INFO 2025-05-25 01:38:01,754 basehttp 2136 25904 "GET /reports/dentist-leaderboard/ HTTP/1.1" 200 52369
INFO 2025-05-25 01:38:11,342 basehttp 2136 25904 "GET /reports/dentist-leaderboard/?start_date=2025-01-25&end_date=2025-05-25 HTTP/1.1" 200 55596
INFO 2025-05-25 01:38:19,111 autoreload 16064 22996 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:38:19,750 autoreload 2136 10192 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:38:20,138 autoreload 9604 17152 Watching for file changes with StatReloader
INFO 2025-05-25 01:38:20,659 autoreload 8268 21084 Watching for file changes with StatReloader
INFO 2025-05-25 01:38:25,251 basehttp 9604 23768 "GET /reports/item-inventory/ HTTP/1.1" 200 85543
INFO 2025-05-25 01:38:30,885 autoreload 9604 17152 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:38:30,949 autoreload 8268 21084 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:38:31,924 autoreload 22112 19648 Watching for file changes with StatReloader
INFO 2025-05-25 01:38:31,958 autoreload 25188 4900 Watching for file changes with StatReloader
INFO 2025-05-25 01:38:41,487 basehttp 25188 23828 "GET /reports/item-usage/ HTTP/1.1" 200 58596
INFO 2025-05-25 01:38:52,767 basehttp 25188 23828 "GET /reports/item-usage/?start_date=2024-04-25&end_date=2025-05-25 HTTP/1.1" 200 71497
INFO 2025-05-25 01:39:01,161 autoreload 25188 4900 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:39:01,621 autoreload 22112 19648 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:39:02,464 autoreload 8984 22816 Watching for file changes with StatReloader
INFO 2025-05-25 01:39:02,774 autoreload 7460 18336 Watching for file changes with StatReloader
INFO 2025-05-25 01:39:26,177 basehttp 8984 9120 "GET /Dentists/ HTTP/1.1" 200 99580
INFO 2025-05-25 01:39:29,250 basehttp 8984 9120 "GET /reports/ HTTP/1.1" 200 64949
INFO 2025-05-25 01:39:32,366 autoreload 7460 18336 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:39:32,727 autoreload 8984 22816 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:39:33,514 autoreload 4644 7088 Watching for file changes with StatReloader
INFO 2025-05-25 01:39:33,782 autoreload 4652 6836 Watching for file changes with StatReloader
INFO 2025-05-25 01:39:35,481 basehttp 4652 26104 "GET /reports/workflow-efficiency/ HTTP/1.1" 200 57848
INFO 2025-05-25 01:40:05,519 autoreload 4644 7088 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:40:05,745 autoreload 4652 6836 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:40:06,683 autoreload 24260 22592 Watching for file changes with StatReloader
INFO 2025-05-25 01:40:06,906 autoreload 23476 20580 Watching for file changes with StatReloader
INFO 2025-05-25 01:40:10,707 basehttp 23476 22316 "GET /reports/workflow-efficiency/?start_date=2024-04-25&end_date=2025-05-25 HTTP/1.1" 200 61142
INFO 2025-05-25 01:40:38,054 autoreload 23476 20580 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:40:38,809 autoreload 24260 22592 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:40:39,371 autoreload 10344 26532 Watching for file changes with StatReloader
INFO 2025-05-25 01:40:39,849 autoreload 22004 10080 Watching for file changes with StatReloader
INFO 2025-05-25 01:40:41,007 basehttp 10344 22000 "GET /reports/ HTTP/1.1" 200 64949
INFO 2025-05-25 01:41:01,639 basehttp 10344 22000 "GET /reports/inventory-optimization/ HTTP/1.1" 200 97204
INFO 2025-05-25 01:41:05,000 autoreload 5360 26548 Watching for file changes with StatReloader
WARNING 2025-05-25 01:41:23,882 log 10344 22000 Not Found: /reports/dashboard/
WARNING 2025-05-25 01:41:23,883 basehttp 10344 22000 "GET /reports/dashboard/ HTTP/1.1" 404 11378
WARNING 2025-05-25 01:41:30,607 log 10344 22000 Not Found: /reports/dashboard/
WARNING 2025-05-25 01:41:30,607 basehttp 10344 22000 "GET /reports/dashboard/ HTTP/1.1" 404 11378
INFO 2025-05-25 01:41:37,808 basehttp 10344 22000 "GET /reports/inventory-optimization/ HTTP/1.1" 200 97204
INFO 2025-05-25 01:44:45,075 autoreload 10344 26532 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:44:46,125 autoreload 6880 3152 Watching for file changes with StatReloader
INFO 2025-05-25 01:44:46,180 autoreload 22004 10080 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:44:47,179 autoreload 20716 18220 Watching for file changes with StatReloader
INFO 2025-05-25 01:45:06,458 autoreload 20716 18220 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:45:06,498 autoreload 6880 3152 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:45:07,452 autoreload 11540 23784 Watching for file changes with StatReloader
INFO 2025-05-25 01:45:07,528 autoreload 8776 10256 Watching for file changes with StatReloader
INFO 2025-05-25 01:45:27,238 basehttp 8776 25692 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:45:29,359 basehttp 8776 25692 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:45:30,989 autoreload 8776 10256 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:45:31,965 autoreload 11540 23784 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:45:32,247 autoreload 11404 20044 Watching for file changes with StatReloader
INFO 2025-05-25 01:45:32,943 autoreload 21844 26352 Watching for file changes with StatReloader
INFO 2025-05-25 01:45:37,086 basehttp 11404 20168 "GET /reports/profitability-analysis/ HTTP/1.1" 200 72929
INFO 2025-05-25 01:45:51,678 basehttp 11404 20168 "GET /reports/profitability-analysis/?start_date=2024-02-24&end_date=2025-05-25 HTTP/1.1" 200 72929
INFO 2025-05-25 01:45:52,988 autoreload 21844 26352 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:45:53,845 autoreload 11404 20044 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:45:54,225 autoreload 22956 3716 Watching for file changes with StatReloader
INFO 2025-05-25 01:45:54,831 autoreload 25628 144 Watching for file changes with StatReloader
INFO 2025-05-25 01:46:14,306 autoreload 22956 3716 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:46:14,450 autoreload 25628 144 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:46:15,403 autoreload 22868 24000 Watching for file changes with StatReloader
INFO 2025-05-25 01:46:15,525 autoreload 23636 23600 Watching for file changes with StatReloader
INFO 2025-05-25 01:46:28,150 basehttp 23636 23752 "GET /reports/ HTTP/1.1" 200 64949
INFO 2025-05-25 01:46:30,501 basehttp 23636 23752 "GET /reports/quality-metrics/ HTTP/1.1" 200 73419
INFO 2025-05-25 01:46:35,469 autoreload 22868 24000 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:46:35,475 autoreload 23636 23600 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:46:36,722 autoreload 7392 17004 Watching for file changes with StatReloader
INFO 2025-05-25 01:46:36,741 autoreload 19560 4696 Watching for file changes with StatReloader
INFO 2025-05-25 01:46:42,003 basehttp 19560 10952 "GET /reports/profitability-analysis/ HTTP/1.1" 200 72929
INFO 2025-05-25 01:46:45,544 basehttp 19560 10952 "GET /reports/demand-forecasting/ HTTP/1.1" 200 57930
INFO 2025-05-25 01:47:04,772 autoreload 7392 17004 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:47:05,061 autoreload 19560 4696 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:47:05,825 autoreload 26020 7224 Watching for file changes with StatReloader
INFO 2025-05-25 01:47:06,073 autoreload 5964 5288 Watching for file changes with StatReloader
INFO 2025-05-25 01:47:28,797 autoreload 26020 7224 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:47:28,855 autoreload 5964 5288 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:47:30,087 autoreload 2136 14904 Watching for file changes with StatReloader
INFO 2025-05-25 01:47:30,132 autoreload 25328 12208 Watching for file changes with StatReloader
INFO 2025-05-25 01:47:32,317 basehttp 25328 24648 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:47:50,503 autoreload 2136 14904 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:47:50,507 autoreload 25328 12208 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:47:51,438 autoreload 20392 23436 Watching for file changes with StatReloader
INFO 2025-05-25 01:47:51,460 autoreload 21464 24136 Watching for file changes with StatReloader
INFO 2025-05-25 01:48:07,009 basehttp 21464 11456 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:48:10,590 basehttp 21464 11456 "GET /reports/profitability-analysis/ HTTP/1.1" 200 72929
INFO 2025-05-25 01:48:19,017 autoreload 21464 24136 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:48:19,454 autoreload 20392 23436 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:48:20,127 autoreload 26432 24144 Watching for file changes with StatReloader
INFO 2025-05-25 01:48:20,406 autoreload 10664 9504 Watching for file changes with StatReloader
INFO 2025-05-25 01:48:21,797 basehttp 26432 23708 "GET /reports/quality-metrics/ HTTP/1.1" 200 73419
INFO 2025-05-25 01:48:36,369 basehttp 26432 23708 "GET /reports/quality-metrics/?start_date=2024-02-24&end_date=2025-05-25 HTTP/1.1" 200 73419
INFO 2025-05-25 01:48:47,636 basehttp 26432 23708 "GET /reports/ HTTP/1.1" 200 64949
INFO 2025-05-25 01:48:49,771 autoreload 26432 24144 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:48:49,879 autoreload 10664 9504 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:48:50,771 autoreload 26444 25816 Watching for file changes with StatReloader
INFO 2025-05-25 01:48:50,834 autoreload 21912 20660 Watching for file changes with StatReloader
INFO 2025-05-25 01:48:55,202 basehttp 26444 10616 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:49:12,247 basehttp 26444 10616 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:49:23,363 basehttp 26444 10616 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:49:28,973 basehttp 26444 10616 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:49:49,710 basehttp 26444 10616 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:49:54,908 basehttp 26444 10616 "GET /reports/workflow-efficiency/ HTTP/1.1" 200 57848
INFO 2025-05-25 01:50:12,050 basehttp 26444 10616 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:50:56,614 basehttp 26444 10616 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:50:59,786 basehttp 26444 10616 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:51:20,354 basehttp 26444 10616 "GET /reports/demand-forecasting/ HTTP/1.1" 200 932
INFO 2025-05-25 01:51:46,949 basehttp 26444 10616 "GET /reports/demand-forecasting/ HTTP/1.1" 200 932
INFO 2025-05-25 01:51:48,175 basehttp 26444 10616 "GET /reports/demand-forecasting/ HTTP/1.1" 200 932
INFO 2025-05-25 01:51:49,471 basehttp 26444 10616 "GET /reports/demand-forecasting/ HTTP/1.1" 200 932
INFO 2025-05-25 01:52:25,417 basehttp 26444 10616 "GET /reports/demand-forecasting/ HTTP/1.1" 200 932
INFO 2025-05-25 01:52:28,274 basehttp 26444 10616 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:52:41,516 autoreload 21912 20660 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:52:41,524 autoreload 26444 25816 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:52:42,487 autoreload 11012 24660 Watching for file changes with StatReloader
INFO 2025-05-25 01:52:42,524 autoreload 3800 24112 Watching for file changes with StatReloader
INFO 2025-05-25 01:52:50,458 basehttp 3800 11612 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:52:52,833 basehttp 3800 11612 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:53:05,938 basehttp 3800 11612 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:53:20,919 autoreload 11012 24660 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:53:21,641 autoreload 3800 24112 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:53:21,818 autoreload 21960 21020 Watching for file changes with StatReloader
INFO 2025-05-25 01:53:22,504 autoreload 9212 17480 Watching for file changes with StatReloader
INFO 2025-05-25 01:53:34,648 autoreload 9212 17480 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:53:35,414 autoreload 21960 21020 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:53:35,624 autoreload 13576 2612 Watching for file changes with StatReloader
INFO 2025-05-25 01:53:36,357 autoreload 24248 17904 Watching for file changes with StatReloader
INFO 2025-05-25 01:53:41,966 basehttp 13576 24048 "GET /reports/inventory-optimization/ HTTP/1.1" 200 936
INFO 2025-05-25 01:53:45,466 basehttp 13576 24048 "GET /reports/profitability-analysis/ HTTP/1.1" 200 72929
INFO 2025-05-25 01:53:49,455 autoreload 24248 17904 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:53:50,045 autoreload 13576 2612 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:53:50,514 autoreload 5996 26604 Watching for file changes with StatReloader
INFO 2025-05-25 01:53:50,985 autoreload 23844 18728 Watching for file changes with StatReloader
INFO 2025-05-25 01:54:03,973 autoreload 23844 18728 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:54:04,028 autoreload 5996 26604 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:54:05,078 autoreload 17004 6160 Watching for file changes with StatReloader
INFO 2025-05-25 01:54:05,104 autoreload 7344 7224 Watching for file changes with StatReloader
INFO 2025-05-25 01:54:17,318 autoreload 17004 6160 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:54:17,494 autoreload 7344 7224 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:54:18,158 autoreload 25468 21052 Watching for file changes with StatReloader
INFO 2025-05-25 01:54:18,300 autoreload 25592 14176 Watching for file changes with StatReloader
INFO 2025-05-25 01:54:29,474 autoreload 25592 14176 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:54:30,279 autoreload 25468 21052 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:54:30,375 autoreload 23424 7276 Watching for file changes with StatReloader
INFO 2025-05-25 01:54:31,174 autoreload 6248 4888 Watching for file changes with StatReloader
INFO 2025-05-25 01:54:52,330 autoreload 6248 4888 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:54:53,127 autoreload 23424 7276 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:54:53,139 autoreload 20492 9448 Watching for file changes with StatReloader
INFO 2025-05-25 01:54:53,947 autoreload 5320 11096 Watching for file changes with StatReloader
INFO 2025-05-25 01:54:59,906 basehttp 20492 6388 "GET /reports/ HTTP/1.1" 200 64949
INFO 2025-05-25 01:55:09,033 basehttp 20492 6388 "GET /reports/inventory-optimization/ HTTP/1.1" 200 88565
INFO 2025-05-25 01:55:11,519 autoreload 5320 11096 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:55:12,041 autoreload 20492 9448 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:55:12,713 autoreload 21864 10172 Watching for file changes with StatReloader
INFO 2025-05-25 01:55:13,123 autoreload 26492 18232 Watching for file changes with StatReloader
INFO 2025-05-25 01:55:24,728 autoreload 21864 10172 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:55:24,771 autoreload 26492 18232 C:\GPT4_PROJECTS\VERSIONE STABEL\DENTAL_LAB - 23-04-2025\reports\views.py changed, reloading.
INFO 2025-05-25 01:55:25,876 autoreload 13732 21076 Watching for file changes with StatReloader
INFO 2025-05-25 01:55:25,894 autoreload 7240 23000 Watching for file changes with StatReloader
INFO 2025-05-25 01:55:47,509 autoreload 11624 24892 Watching for file changes with StatReloader
INFO 2025-05-25 01:56:06,632 basehttp 7240 23504 "GET /reports/inventory-optimization/ HTTP/1.1" 200 88565
