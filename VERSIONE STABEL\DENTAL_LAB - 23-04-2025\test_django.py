#!/usr/bin/env python
"""
Simple Django test script to verify the setup works
"""
import os
import sys
import django
from django.conf import settings

# Add the project directory to Python path
project_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_dir)

# Set the Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')

try:
    # Setup Django
    django.setup()
    print("✅ Django setup successful!")
    
    # Test imports
    from accounts.views import home_view
    print("✅ Views import successful!")
    
    from accounts.models import CustomUser
    print("✅ Models import successful!")
    
    # Test database connection
    from django.db import connection
    with connection.cursor() as cursor:
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        if result:
            print("✅ Database connection successful!")
    
    # Test static files configuration
    from django.contrib.staticfiles import finders
    css_file = finders.find('css/dashboard.css')
    if css_file:
        print(f"✅ CSS file found at: {css_file}")
    else:
        print("❌ CSS file not found!")
    
    print("\n🎉 All tests passed! Django is working correctly.")
    print("You can now run: python manage.py runserver")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
