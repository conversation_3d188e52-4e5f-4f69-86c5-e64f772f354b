<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard CSS Test</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="static/css/dashboard.css">
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-content">
            <!-- Premium Dashboard Header -->
            <header class="dashboard-header">
                <div class="dashboard-header-content">
                    <div class="dashboard-title-section">
                        <h1 class="dashboard-title">Dental Lab Dashboard</h1>
                        <p class="dashboard-subtitle">
                            <i class="bi bi-activity"></i>
                            Real-time insights and case management
                        </p>
                    </div>
                    <div class="dashboard-actions">
                        <div class="search-container">
                            <div class="search-input-wrapper">
                                <i class="bi bi-search"></i>
                                <input type="text" class="search-input" placeholder="Search cases, patients, dentists..." id="globalSearch">
                            </div>
                        </div>
                        <div class="action-buttons">
                            <button class="btn-action" title="Refresh Data" id="refreshBtn">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                            <button class="btn-action btn-primary" title="Dark Mode Toggle" id="darkModeToggle">
                                <i class="bi bi-moon-stars"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Metrics Grid -->
            <div class="metrics-grid">
                <div class="metric-card accent-primary">
                    <div class="metric-card-content">
                        <div class="metric-icon icon-primary"><i class="bi bi-folder2-open"></i></div>
                        <div class="metric-content">
                            <span class="metric-label">Total Cases</span>
                            <div class="metric-value">1,234</div>
                            <div class="metric-comparison"><i class="bi bi-info-circle"></i> All-time record</div>
                        </div>
                    </div>
                </div>

                <div class="metric-card accent-success">
                    <div class="metric-card-content">
                        <div class="metric-icon icon-success"><i class="bi bi-calendar-plus"></i></div>
                        <div class="metric-content">
                            <span class="metric-label">Cases Today</span>
                            <div class="metric-value">42</div>
                            <div class="metric-comparison">New cases received</div>
                        </div>
                    </div>
                </div>

                <div class="metric-card accent-info">
                    <div class="metric-card-content">
                        <div class="metric-icon icon-info"><i class="bi bi-calendar-week"></i></div>
                        <div class="metric-content">
                            <span class="metric-label">This Week</span>
                            <div class="metric-value">156</div>
                            <div class="metric-comparison">
                                <span class="trend-up"><i class="bi bi-arrow-up-short"></i> 12 more</span>
                                than last week
                            </div>
                        </div>
                    </div>
                </div>

                <div class="metric-card accent-warning">
                    <div class="metric-card-content">
                        <div class="metric-icon icon-warning"><i class="bi bi-calendar-month"></i></div>
                        <div class="metric-content">
                            <span class="metric-label">This Month</span>
                            <div class="metric-value">678</div>
                            <div class="metric-comparison">
                                <span class="trend-up"><i class="bi bi-arrow-up-short"></i> 45 more</span>
                                than last month
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ready to Ship Banner -->
            <div class="ready-to-ship-banner">
                <div class="banner-content">
                    <div class="banner-icon"><i class="bi bi-box-seam"></i></div>
                    <div class="banner-text">
                        <h3>Ready to Ship Cases</h3>
                        <p>You have <strong>8 cases</strong> ready for shipment. Process them for timely delivery.</p>
                    </div>
                </div>
                <div class="banner-action">
                    <a href="#" class="btn-ship">Process Shipments <i class="bi bi-arrow-right"></i></a>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        const htmlElement = document.documentElement;
        
        darkModeToggle.addEventListener('click', () => {
            const currentTheme = htmlElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            htmlElement.setAttribute('data-theme', newTheme);
            
            const icon = darkModeToggle.querySelector('i');
            icon.className = newTheme === 'dark' ? 'bi bi-sun' : 'bi bi-moon-stars';
        });
    </script>
</body>
</html>
