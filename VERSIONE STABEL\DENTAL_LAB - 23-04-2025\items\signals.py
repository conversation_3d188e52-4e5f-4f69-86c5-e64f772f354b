from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.utils import timezone  # Import timezone instead of datetime
import logging

from .models import (
    RawMaterialInventory,
    Inventory,
    RawMaterialInventoryHistory,
    ItemRawMaterial
)
from case.models import CaseItem
from billing.models import PurchaseOrderItem

logger = logging.getLogger(__name__)

@receiver(pre_save, sender=CaseItem)
def store_previous_quantity(sender, instance, **kwargs):
    if instance.pk:
        old_instance = CaseItem.objects.get(pk=instance.pk)
        instance.old_quantity = old_instance.quantity
    else:
        instance.old_quantity = 0

@receiver(post_save, sender=CaseItem)
def update_raw_material_inventory_on_case_item_save(sender, instance, created, **kwargs):
    if created:
        update_raw_material_inventory(instance, created=True)
    else:
        update_raw_material_inventory(instance, created=False)

@receiver(post_delete, sender=CaseItem)
def update_raw_material_inventory_on_case_item_delete(sender, instance, **kwargs):
    update_raw_material_inventory(instance, delete=True)

def update_raw_material_inventory(instance, created=False, delete=False):
    try:
        item = instance.item
        item_quantity = instance.quantity
        old_quantity = getattr(instance, 'old_quantity', 0)

        item_raw_materials = item.itemrawmaterial_set.all()

        for item_raw_material in item_raw_materials:
            raw_material = item_raw_material.raw_material
            raw_material_quantity_per_item = item_raw_material.quantity

            if delete:
                total_raw_material_used = old_quantity * raw_material_quantity_per_item
            else:
                quantity_diff = item_quantity - old_quantity
                total_raw_material_used = quantity_diff * raw_material_quantity_per_item

            raw_material_inventory, _ = RawMaterialInventory.objects.get_or_create(
                raw_material=raw_material,
                unit=raw_material.unit,  # Include unit in get_or_create
                defaults={'quantity': 0}
            )

            if created or not delete:
                raw_material_inventory.quantity -= total_raw_material_used
            else:
                raw_material_inventory.quantity += total_raw_material_used

            raw_material_inventory.save()

            history_change_type = 'case_delete' if delete else 'case'
            RawMaterialInventoryHistory.objects.create(
                raw_material_inventory=raw_material_inventory,
                change_type=history_change_type,
                change_quantity=-total_raw_material_used if delete else total_raw_material_used,
                case_item=instance if isinstance(instance, CaseItem) else None,
                timestamp=timezone.now()  # Use timezone.now() instead of now()
            )
    except Exception as e:
        logger.exception("Error updating raw material inventory: %s", str(e))

@receiver(post_save, sender=CaseItem)
def update_item_inventory_on_case_item_save(sender, instance, created, **kwargs):
    if created:
        update_item_inventory(instance, created=True)
    else:
        update_item_inventory(instance, created=False)

@receiver(post_delete, sender=CaseItem)
def update_item_inventory_on_case_item_delete(sender, instance, **kwargs):
    update_item_inventory(instance, delete=True)

def update_item_inventory(instance, created=False, delete=False):
    try:
        item = instance.item
        item_quantity = instance.quantity
        old_quantity = getattr(instance, 'old_quantity', 0)

        unit = item.unit
        item_inventory, _ = Inventory.objects.get_or_create(
            item=item,
            defaults={'quantity': 0, 'unit': unit}
        )

        if created:
            item_inventory.quantity -= item_quantity
        elif delete:
            item_inventory.quantity += item_quantity
        else:
            quantity_diff = item_quantity - old_quantity
            item_inventory.quantity -= quantity_diff

        item_inventory.save()
    except Exception as e:
        logger.exception("Error updating item inventory: %s", str(e))

@receiver(post_save, sender=PurchaseOrderItem)
def update_raw_material_inventory_on_purchase(sender, instance, **kwargs):
    """
    Update inventory using IFRS/SKK compliant costing methods
    """
    try:
        created = kwargs.get('created', False)

        if created:
            # Use the new IFRS/SKK compliant costing service
            from items.services.inventory_costing import InventoryCostingService
            costing_service = InventoryCostingService()
            costing_service.update_inventory_on_purchase(instance)
            logger.info(f"Updated inventory using costing service for {instance.raw_material.name}")
        else:
            # Handle updates to existing purchase order items
            # For now, we'll use the old logic but this should be enhanced
            logger.warning(f"Purchase order item update not fully implemented for costing: {instance}")

    except Exception as e:
        logger.exception("Error updating raw material inventory on purchase: %s", str(e))

@receiver(post_delete, sender=PurchaseOrderItem)
def update_raw_material_inventory_on_purchase_delete(sender, instance, **kwargs):
    try:
        raw_material_inventory = RawMaterialInventory.objects.get(
            raw_material=instance.raw_material,
            unit=instance.unit
        )
        raw_material_inventory.quantity -= instance.quantity
        raw_material_inventory.save()

        # Add history entry for deletion
        RawMaterialInventoryHistory.objects.create(
            raw_material_inventory=raw_material_inventory,
            change_type='purchase_delete',
            change_quantity=-instance.quantity,
            purchase_order_item=instance,
            timestamp=timezone.now()
        )
    except Exception as e:
        logger.exception("Error updating raw material inventory on purchase delete: %s", str(e))