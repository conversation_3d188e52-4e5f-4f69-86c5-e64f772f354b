{% extends 'base.html' %}
{% load static %}
{% load humanize %}
{% load common_filters %}

{% block title %}Raw Materials Demand Forecasting Report{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
<style>
    .forecast-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    .forecast-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .forecast-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .capacity-recommendation {
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 8px;
        border-left: 4px solid;
    }
    .capacity-increase {
        background-color: #f8d7da;
        border-left-color: #dc3545;
        color: #721c24;
    }
    .capacity-optimal {
        background-color: #d4edda;
        border-left-color: #28a745;
        color: #155724;
    }
    .seasonal-month {
        background: white;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
    .confidence-high { color: #28a745; }
    .confidence-medium { color: #ffc107; }
    .confidence-low { color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-crystal-ball"></i> Raw Materials Demand Forecasting Report
            </h1>

            <!-- Analysis Period Info -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Analysis Period</h6>
                            <p class="mb-0">{{ start_date|date:"M d, Y" }} to {{ end_date|date:"M d, Y" }}</p>
                            <small class="text-muted">Historical data used for forecasting</small>
                        </div>
                        <div class="col-md-6">
                            <h6>Forecast Horizon</h6>
                            <p class="mb-0">Next 6 months</p>
                            <small class="text-muted">Confidence decreases over time</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Forecast KPIs -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="forecast-card">
                <div class="forecast-value">{{ growth_rate|floatformat:1 }}%</div>
                <div class="forecast-label">Growth Rate (6-month avg)</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="forecast-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="forecast-value">
                    {% if forecast_data %}{{ forecast_data.0.forecast_cases }}{% else %}N/A{% endif %}
                </div>
                <div class="forecast-label">Next Month Forecast</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="forecast-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="forecast-value">
                    {% if trend_slope > 0 %}↗{% elif trend_slope < 0 %}↘{% else %}→{% endif %}
                </div>
                <div class="forecast-label">Trend Direction</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="forecast-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="forecast-value">{{ procurement_recommendations|length }}</div>
                <div class="forecast-label">Procurement Recommendations</div>
            </div>
        </div>
    </div>

    <!-- Forecast Chart and Seasonal Patterns -->
    <div class="row">
        <!-- Demand Forecast Chart -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Historical Data & Forecast</h5>
                </div>
                <div class="card-body">
                    <canvas id="forecastChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Seasonal Patterns -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calendar-alt"></i> Seasonal Patterns</h5>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    {% for month, data in seasonal_patterns.items %}
                    <div class="seasonal-month">
                        <h6>{{ month|date:"F" }}</h6>
                        <div class="h4 text-primary">{{ data.avg_cases|floatformat:0 }}</div>
                        <small class="text-muted">avg cases ({{ data.data_points }} data points)</small>
                    </div>
                    {% empty %}
                    <p class="text-muted">Insufficient data for seasonal analysis.</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Forecast Details and Capacity Planning -->
    <div class="row mt-4">
        <!-- Detailed Forecast -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-list"></i> 6-Month Forecast Details</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Month</th>
                                    <th>Forecast Cases</th>
                                    <th>Confidence</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for forecast in forecast_data %}
                                <tr>
                                    <td><strong>{{ forecast.month|date:"M Y" }}</strong></td>
                                    <td>{{ forecast.forecast_cases }}</td>
                                    <td>
                                        <span class="
                                            {% if forecast.confidence >= 0.8 %}confidence-high
                                            {% elif forecast.confidence >= 0.6 %}confidence-medium
                                            {% else %}confidence-low{% endif %}">
                                            {{ forecast.confidence|multiply:100|floatformat:0 }}%
                                        </span>
                                    </td>
                                    <td>
                                        {% if forecast.confidence >= 0.8 %}
                                        <span class="badge bg-success">High Confidence</span>
                                        {% elif forecast.confidence >= 0.6 %}
                                        <span class="badge bg-warning">Medium Confidence</span>
                                        {% else %}
                                        <span class="badge bg-danger">Low Confidence</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center text-muted">
                                        Insufficient historical data for forecasting
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Procurement Recommendations -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-shopping-cart"></i> Raw Materials Procurement Recommendations</h5>
                </div>
                <div class="card-body">
                    {% for recommendation in procurement_recommendations %}
                    <div class="capacity-recommendation
                        {% if recommendation.urgency == 'High' %}capacity-increase
                        {% else %}capacity-optimal{% endif %}">
                        <h6>{{ recommendation.type }}</h6>
                        <div class="row">
                            <div class="col-6">
                                <small>Raw Material</small><br>
                                <strong>{{ recommendation.raw_material }}</strong>
                            </div>
                            <div class="col-6">
                                <small>Procurement Needed</small><br>
                                <strong>{{ recommendation.procurement_needed|floatformat:2 }} {{ recommendation.unit }}</strong>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-6">
                                <small>Current Stock</small><br>
                                <span>{{ recommendation.current_stock|floatformat:2 }} {{ recommendation.unit }}</span>
                            </div>
                            <div class="col-6">
                                <small>Urgency</small><br>
                                <span class="badge {% if recommendation.urgency == 'High' %}bg-danger{% else %}bg-warning{% endif %}">
                                    {{ recommendation.urgency }}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Current raw materials stock appears adequate for forecasted demand.
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Raw Materials Demand Forecast -->
    {% if raw_material_forecasts %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-industry"></i> Raw Materials Demand Forecast</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Raw Material</th>
                                    <th>Unit</th>
                                    <th>Usage per Case</th>
                                    <th>Next Month</th>
                                    <th>3 Months</th>
                                    <th>6 Months</th>
                                    <th>Total 6-Month Demand</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rm_forecast in raw_material_forecasts %}
                                <tr>
                                    <td><strong>{{ rm_forecast.raw_material.name }}</strong></td>
                                    <td>{{ rm_forecast.unit }}</td>
                                    <td>{{ rm_forecast.usage_per_case|floatformat:3 }}</td>
                                    <td>
                                        {% if rm_forecast.forecasts.0 %}
                                        {{ rm_forecast.forecasts.0.forecast_usage|floatformat:2 }}
                                        {% else %}N/A{% endif %}
                                    </td>
                                    <td>
                                        {% if rm_forecast.forecasts.2 %}
                                        {{ rm_forecast.forecasts.2.forecast_usage|floatformat:2 }}
                                        {% else %}N/A{% endif %}
                                    </td>
                                    <td>
                                        {% if rm_forecast.forecasts.5 %}
                                        {{ rm_forecast.forecasts.5.forecast_usage|floatformat:2 }}
                                        {% else %}N/A{% endif %}
                                    </td>
                                    <td>
                                        <strong>
                                        {% with total=0 %}
                                        {% for forecast in rm_forecast.forecasts %}
                                            {% with total=total|add:forecast.forecast_usage %}{% endwith %}
                                        {% endfor %}
                                        {{ total|floatformat:2 }}
                                        {% endwith %}
                                        </strong>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Historical Trends Analysis -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-area"></i> Historical Trends Analysis</h5>
                </div>
                <div class="card-body">
                    <canvas id="historicalTrendsChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Forecast Insights -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> Forecast Insights</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Key Insights</h6>
                        <ul class="mb-0">
                            {% if growth_rate > 5 %}
                            <li>Strong growth trend detected ({{ growth_rate|floatformat:1 }}%)</li>
                            {% elif growth_rate < -5 %}
                            <li>Declining trend observed ({{ growth_rate|floatformat:1 }}%)</li>
                            {% else %}
                            <li>Stable demand pattern</li>
                            {% endif %}
                            <li>Seasonal variations identified</li>
                            <li>Forecast confidence varies by period</li>
                        </ul>
                    </div>

                    {% if growth_rate > 10 %}
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Action Required</h6>
                        <p class="mb-0">High growth rate may require capacity expansion planning.</p>
                    </div>
                    {% endif %}

                    <div class="alert alert-success">
                        <h6><i class="fas fa-target"></i> Planning Recommendations</h6>
                        <ul class="mb-0">
                            <li>Review forecasts monthly</li>
                            <li>Plan capacity 3 months ahead</li>
                            <li>Monitor actual vs forecast</li>
                            <li>Adjust for seasonal patterns</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6>Export Options</h6>
                    <a href="#" class="btn btn-success me-2" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> Export Forecast to Excel
                    </a>
                    <a href="#" class="btn btn-danger" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i> Export Report to PDF
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script>
// Forecast Chart
const forecastCtx = document.getElementById('forecastChart').getContext('2d');
const forecastChart = new Chart(forecastCtx, {
    type: 'line',
    data: {
        labels: [
            {% for data in historical_data %}
            '{{ data.month|date:"M Y" }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
            {% if forecast_data %}
            {% for forecast in forecast_data %}
            ,'{{ forecast.month|date:"M Y" }}'
            {% endfor %}
            {% endif %}
        ],
        datasets: [{
            label: 'Historical Cases',
            data: [
                {% for data in historical_data %}
                {{ data.case_count|default:0 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
                {% if forecast_data %}
                {% for forecast in forecast_data %}
                ,null
                {% endfor %}
                {% endif %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            fill: false
        }, {
            label: 'Forecast',
            data: [
                {% for data in historical_data %}
                null{% if not forloop.last %},{% endif %}
                {% endfor %}
                {% if forecast_data %}
                {% for forecast in forecast_data %}
                ,{{ forecast.forecast_cases }}
                {% endfor %}
                {% endif %}
            ],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            borderDash: [5, 5],
            tension: 0.1,
            fill: false
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Number of Cases'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'Time Period'
                }
            }
        },
        plugins: {
            legend: {
                display: true
            }
        }
    }
});

// Historical Trends Chart
const historicalCtx = document.getElementById('historicalTrendsChart').getContext('2d');
const historicalChart = new Chart(historicalCtx, {
    type: 'bar',
    data: {
        labels: [
            {% for data in historical_data|slice:"-12:" %}
            '{{ data.month|date:"M Y" }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Cases',
            data: [
                {% for data in historical_data|slice:"-12:" %}
                {{ data.case_count|default:0 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Number of Cases'
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

function exportToExcel() {
    alert('Excel export functionality will be implemented');
}

function exportToPDF() {
    alert('PDF export functionality will be implemented');
}
</script>
{% endblock %}
