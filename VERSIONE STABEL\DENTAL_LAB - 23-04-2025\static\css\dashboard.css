/*
 * DENTAL LAB DASHBOARD - PREMIUM DESIGN SYSTEM
 * Modern, Professional, High-Performance Dashboard
 */

:root {
    /* === PREMIUM COLOR SYSTEM === */

    /* Primary Brand Colors */
    --dental-blue: #2563eb;
    --dental-blue-light: #3b82f6;
    --dental-blue-dark: #1d4ed8;
    --dental-blue-50: #eff6ff;
    --dental-blue-100: #dbeafe;
    --dental-blue-500: #3b82f6;
    --dental-blue-600: #2563eb;
    --dental-blue-700: #1d4ed8;
    --dental-blue-900: #1e3a8a;

    /* Success Colors */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;

    /* Warning Colors */
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-500: #f59e0b;
    --warning-600: #d97706;

    /* Danger Colors */
    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-500: #ef4444;
    --danger-600: #dc2626;

    /* Info Colors */
    --info-50: #f0f9ff;
    --info-100: #e0f2fe;
    --info-500: #06b6d4;
    --info-600: #0891b2;

    /* Purple Colors */
    --purple-50: #faf5ff;
    --purple-100: #f3e8ff;
    --purple-500: #a855f7;
    --purple-600: #9333ea;

    /* === NEUTRAL COLORS === */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* === SEMANTIC COLORS (LIGHT MODE) === */
    --primary: var(--dental-blue-600);
    --primary-hover: var(--dental-blue-700);
    --primary-light: var(--dental-blue-50);
    --primary-alpha: rgba(37, 99, 235, 0.1);

    --success: var(--success-600);
    --success-light: var(--success-50);
    --success-alpha: rgba(34, 197, 94, 0.1);

    --warning: var(--warning-600);
    --warning-light: var(--warning-50);
    --warning-alpha: rgba(245, 158, 11, 0.1);

    --danger: var(--danger-600);
    --danger-light: var(--danger-50);
    --danger-alpha: rgba(239, 68, 68, 0.1);

    --info: var(--info-600);
    --info-light: var(--info-50);
    --info-alpha: rgba(6, 182, 212, 0.1);

    --purple: var(--purple-600);
    --purple-light: var(--purple-50);
    --purple-alpha: rgba(168, 85, 247, 0.1);

    /* === BACKGROUND & SURFACE === */
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);
    --surface: #ffffff;
    --surface-elevated: #ffffff;

    /* === TEXT COLORS === */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-tertiary: var(--gray-500);
    --text-inverse: #ffffff;

    /* === BORDERS === */
    --border-primary: var(--gray-200);
    --border-secondary: var(--gray-100);
    --border-focus: var(--dental-blue-500);

    /* === SHADOWS === */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* === SPACING SYSTEM === */
    --space-0: 0;
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */

    /* === BORDER RADIUS === */
    --radius-none: 0;
    --radius-sm: 0.125rem;   /* 2px */
    --radius-base: 0.25rem;  /* 4px */
    --radius-md: 0.375rem;   /* 6px */
    --radius-lg: 0.5rem;     /* 8px */
    --radius-xl: 0.75rem;    /* 12px */
    --radius-2xl: 1rem;      /* 16px */
    --radius-3xl: 1.5rem;    /* 24px */
    --radius-full: 9999px;

    /* === TYPOGRAPHY === */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, monospace;

    --font-size-xs: 0.75rem;     /* 12px */
    --font-size-sm: 0.875rem;    /* 14px */
    --font-size-base: 1rem;      /* 16px */
    --font-size-lg: 1.125rem;    /* 18px */
    --font-size-xl: 1.25rem;     /* 20px */
    --font-size-2xl: 1.5rem;     /* 24px */
    --font-size-3xl: 1.875rem;   /* 30px */
    --font-size-4xl: 2.25rem;    /* 36px */
    --font-size-5xl: 3rem;       /* 48px */

    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    /* === TRANSITIONS === */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* === Z-INDEX === */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
}

/* === DARK MODE THEME === */
[data-theme="dark"] {
    /* Dark Mode Color Overrides */
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-tertiary: var(--gray-700);
    --surface: var(--gray-800);
    --surface-elevated: var(--gray-700);

    --text-primary: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-400);

    --border-primary: var(--gray-700);
    --border-secondary: var(--gray-800);

    /* Adjust alpha colors for dark mode */
    --primary-alpha: rgba(59, 130, 246, 0.15);
    --success-alpha: rgba(34, 197, 94, 0.15);
    --warning-alpha: rgba(245, 158, 11, 0.15);
    --danger-alpha: rgba(239, 68, 68, 0.15);
    --info-alpha: rgba(6, 182, 212, 0.15);
    --purple-alpha: rgba(168, 85, 247, 0.15);
}

/* === GLOBAL RESET & BASE STYLES === */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family-sans);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    transition: background-color var(--transition-base), color var(--transition-base);
}

/* === LAYOUT CONTAINERS === */
.dashboard-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.dashboard-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-6) var(--space-4);
}

/* === PREMIUM DASHBOARD HEADER === */
.dashboard-header {
    background: var(--surface);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    margin-bottom: var(--space-8);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--info) 50%, var(--purple) 100%);
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.dashboard-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--space-6);
}

.dashboard-title-section {
    flex: 1;
    min-width: 300px;
}

.dashboard-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-extrabold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    background: linear-gradient(135deg, var(--primary) 0%, var(--purple) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: var(--line-height-tight);
}

.dashboard-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.dashboard-subtitle i {
    color: var(--primary);
    font-size: var(--font-size-base);
}

.dashboard-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

/* === PREMIUM SEARCH INPUT === */
.search-container {
    position: relative;
    min-width: 280px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--bg-secondary);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-3) var(--space-4);
    transition: all var(--transition-base);
    backdrop-filter: blur(10px);
}

.search-input-wrapper:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 4px var(--primary-alpha);
    transform: translateY(-1px);
}

.search-input-wrapper i {
    color: var(--text-tertiary);
    margin-right: var(--space-3);
    font-size: var(--font-size-lg);
    transition: color var(--transition-base);
}

.search-input-wrapper:focus-within i {
    color: var(--primary);
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    outline: none;
    padding: var(--space-1) 0;
}

.search-input::placeholder {
    color: var(--text-tertiary);
    font-weight: var(--font-weight-normal);
}

.search-input[type="text"]:focus {
    outline: none;
}

/* Select dropdown styling */
.search-input select,
select.search-input {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
    cursor: pointer;
}

.search-input select:focus,
select.search-input:focus {
    outline: none;
}

/* === PREMIUM ACTION BUTTONS === */
.action-buttons {
    display: flex;
    gap: var(--space-3);
}

.btn-action {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-xl);
    border: 2px solid var(--border-primary);
    background: var(--surface);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn-action:hover::before {
    left: 100%;
}

.btn-action:hover {
    border-color: var(--primary);
    color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-action:active {
    transform: translateY(0);
}

.btn-action.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-hover) 100%);
    border-color: var(--primary);
    color: var(--text-inverse);
}

.btn-action.btn-primary:hover {
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    transform: translateY(-3px);
}

/* === PREMIUM METRICS GRID === */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-10);
}

.metric-card {
    background: var(--surface);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--info) 100%);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--transition-base);
}

.metric-card:hover::before {
    transform: scaleX(1);
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-alpha);
}

.metric-card.accent-primary::before {
    background: linear-gradient(90deg, var(--primary) 0%, var(--dental-blue-light) 100%);
}

.metric-card.accent-success::before {
    background: linear-gradient(90deg, var(--success) 0%, var(--success-500) 100%);
}

.metric-card.accent-warning::before {
    background: linear-gradient(90deg, var(--warning) 0%, var(--warning-500) 100%);
}

.metric-card.accent-danger::before {
    background: linear-gradient(90deg, var(--danger) 0%, var(--danger-500) 100%);
}

.metric-card.accent-info::before {
    background: linear-gradient(90deg, var(--info) 0%, var(--info-500) 100%);
}

.metric-card.accent-purple::before {
    background: linear-gradient(90deg, var(--purple) 0%, var(--purple-500) 100%);
}

.metric-card-content {
    display: flex;
    align-items: flex-start;
    gap: var(--space-4);
}

.metric-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.metric-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    opacity: 0.1;
    border-radius: inherit;
}

.metric-icon i {
    position: relative;
    z-index: 1;
}

.icon-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--dental-blue-light) 100%);
    color: var(--text-inverse);
}

.icon-success {
    background: linear-gradient(135deg, var(--success) 0%, var(--success-500) 100%);
    color: var(--text-inverse);
}

.icon-warning {
    background: linear-gradient(135deg, var(--warning) 0%, var(--warning-500) 100%);
    color: var(--text-inverse);
}

.icon-danger {
    background: linear-gradient(135deg, var(--danger) 0%, var(--danger-500) 100%);
    color: var(--text-inverse);
}

.icon-info {
    background: linear-gradient(135deg, var(--info) 0%, var(--info-500) 100%);
    color: var(--text-inverse);
}

.icon-purple {
    background: linear-gradient(135deg, var(--purple) 0%, var(--purple-500) 100%);
    color: var(--text-inverse);
}

.metric-content {
    flex: 1;
    min-width: 0;
}

.metric-label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--space-2);
    display: block;
}

.metric-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-extrabold);
    color: var(--text-primary);
    line-height: var(--line-height-tight);
    margin-bottom: var(--space-3);
    display: flex;
    align-items: baseline;
    gap: var(--space-2);
}

.metric-value .unit {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--text-tertiary);
}

.metric-comparison {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-weight: var(--font-weight-medium);
}

.trend-up {
    color: var(--success);
    font-weight: var(--font-weight-semibold);
}

.trend-down {
    color: var(--danger);
    font-weight: var(--font-weight-semibold);
}

.trend-neutral {
    color: var(--text-tertiary);
    font-weight: var(--font-weight-medium);
}

.metric-comparison i {
    font-size: var(--font-size-base);
}

/* === PREMIUM CARDS === */
.card {
    background: var(--surface);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
    overflow: hidden;
    backdrop-filter: blur(10px);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-alpha);
}

.card-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--border-primary);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--surface) 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.card-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.card-title i {
    color: var(--primary);
    font-size: var(--font-size-lg);
    padding: var(--space-2);
    background: var(--primary-alpha);
    border-radius: var(--radius-lg);
}

.card-body {
    padding: var(--space-6);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.card-body.p-0 {
    padding: 0;
}

/* === PREMIUM CHARTS === */
.chart-container {
    position: relative;
    height: 400px;
    width: 100%;
    padding: var(--space-4);
}

.chart-container-doughnut {
    position: relative;
    height: 320px;
    max-width: 320px;
    margin: 0 auto;
    padding: var(--space-4);
}

/* === PREMIUM TAB CONTROLS === */
.tab-controls {
    display: inline-flex;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-1);
    margin-bottom: var(--space-6);
}

.tab-control {
    border: none;
    background: transparent;
    padding: var(--space-3) var(--space-5);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    color: var(--text-secondary);
    position: relative;
    overflow: hidden;
}

.tab-control::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-hover) 100%);
    opacity: 0;
    transition: opacity var(--transition-base);
    border-radius: inherit;
}

.tab-control.active::before {
    opacity: 1;
}

.tab-control.active {
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.tab-control span {
    position: relative;
    z-index: 1;
}

.tab-control:hover:not(.active) {
    background: var(--primary-alpha);
    color: var(--primary);
}

/* === PREMIUM TABLES === */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-primary);
}

.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: var(--font-size-sm);
    background: var(--surface);
}

.data-table th,
.data-table td {
    padding: var(--space-4) var(--space-5);
    text-align: left;
    border-bottom: 1px solid var(--border-primary);
    vertical-align: middle;
}

.data-table th {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    font-size: var(--font-size-xs);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table th:first-child {
    border-top-left-radius: var(--radius-xl);
}

.data-table th:last-child {
    border-top-right-radius: var(--radius-xl);
}

.data-table tbody tr {
    transition: all var(--transition-base);
    position: relative;
}

.data-table tbody tr:hover {
    background: var(--primary-alpha);
    transform: scale(1.01);
}

.data-table tbody tr:last-child td:first-child {
    border-bottom-left-radius: var(--radius-xl);
}

.data-table tbody tr:last-child td:last-child {
    border-bottom-right-radius: var(--radius-xl);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

.case-number {
    font-weight: var(--font-weight-bold);
    color: var(--primary);
    text-decoration: none;
    padding: var(--space-2) var(--space-3);
    background: var(--primary-alpha);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    display: inline-block;
}

.case-number:hover {
    background: var(--primary);
    color: var(--text-inverse);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* === PREMIUM STATUS BADGES === */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    gap: var(--space-2);
    border: 1px solid transparent;
    transition: all var(--transition-base);
}

.status-badge i {
    font-size: var(--font-size-xs);
}

.status-pending {
    background: var(--warning-light);
    color: var(--warning);
    border-color: var(--warning-alpha);
}

.status-in-progress {
    background: var(--info-light);
    color: var(--info);
    border-color: var(--info-alpha);
}

.status-completed {
    background: var(--success-light);
    color: var(--success);
    border-color: var(--success-alpha);
}

.status-on-hold {
    background: var(--danger-light);
    color: var(--danger);
    border-color: var(--danger-alpha);
}

.status-ready-to-ship {
    background: var(--purple-light);
    color: var(--purple);
    border-color: var(--purple-alpha);
}

.status-shipped {
    background: var(--success-light);
    color: var(--success);
    border-color: var(--success-alpha);
}

.status-delivered {
    background: var(--success-light);
    color: var(--success);
    border-color: var(--success-alpha);
}

.status-closed {
    background: var(--gray-100);
    color: var(--gray-600);
    border-color: var(--gray-200);
}

/* === PREMIUM DENTIST INFO === */
.dentist-info {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.avatar {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    color: var(--text-inverse);
    font-size: var(--font-size-lg);
    flex-shrink: 0;
    text-transform: uppercase;
    border: 2px solid var(--border-primary);
    position: relative;
    overflow: hidden;
}

.avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.2) 100%);
    border-radius: inherit;
}

.avatar-A, .avatar-B, .avatar-C { background: linear-gradient(135deg, var(--primary) 0%, var(--dental-blue-light) 100%); }
.avatar-D, .avatar-E, .avatar-F { background: linear-gradient(135deg, var(--success) 0%, var(--success-500) 100%); }
.avatar-G, .avatar-H, .avatar-I { background: linear-gradient(135deg, var(--info) 0%, var(--info-500) 100%); }
.avatar-J, .avatar-K, .avatar-L { background: linear-gradient(135deg, var(--warning) 0%, var(--warning-500) 100%); }
.avatar-M, .avatar-N, .avatar-O { background: linear-gradient(135deg, var(--danger) 0%, var(--danger-500) 100%); }
.avatar-P, .avatar-Q, .avatar-R { background: linear-gradient(135deg, var(--purple) 0%, var(--purple-500) 100%); }
.avatar-S, .avatar-T, .avatar-U { background: linear-gradient(135deg, var(--primary) 0%, var(--dental-blue-light) 100%); }
.avatar-V, .avatar-W, .avatar-X { background: linear-gradient(135deg, var(--success) 0%, var(--success-500) 100%); }
.avatar-Y, .avatar-Z { background: linear-gradient(135deg, var(--info) 0%, var(--info-500) 100%); }

.dentist-details {
    flex: 1;
    min-width: 0;
}

.dentist-name {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    display: block;
    margin-bottom: var(--space-1);
    font-size: var(--font-size-base);
}

.dentist-clinic {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    display: block;
}

/* === PREMIUM GRID SYSTEM === */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 calc(-1 * var(--space-3));
}

.col-12, .col-lg-4, .col-lg-6, .col-lg-8 {
    width: 100%;
    padding: 0 var(--space-3);
    margin-bottom: var(--space-6);
}

@media (min-width: 992px) {
    .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
}

/* === PREMIUM NOTIFICATIONS === */
.ready-to-ship-banner {
    background: linear-gradient(135deg, var(--success-light) 0%, var(--surface) 100%);
    border: 2px solid var(--success-alpha);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    margin-bottom: var(--space-8);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.ready-to-ship-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--success) 0%, var(--success-500) 100%);
}

.banner-content {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    flex-wrap: wrap;
}

.banner-icon {
    width: 72px;
    height: 72px;
    background: linear-gradient(135deg, var(--success) 0%, var(--success-500) 100%);
    border-radius: var(--radius-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-inverse);
    font-size: var(--font-size-3xl);
    flex-shrink: 0;
    box-shadow: var(--shadow-lg);
}

.banner-text {
    flex: 1;
    min-width: 300px;
}

.banner-text h3 {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.banner-text p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
    line-height: var(--line-height-relaxed);
}

.banner-text strong {
    color: var(--success);
    font-weight: var(--font-weight-bold);
}

.banner-action {
    flex-shrink: 0;
}

.btn-ship {
    background: linear-gradient(135deg, var(--success) 0%, var(--success-500) 100%);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-xl);
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all var(--transition-base);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-3);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.btn-ship::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-slow);
}

.btn-ship:hover::before {
    left: 100%;
}

.btn-ship:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(34, 197, 94, 0.4);
    text-decoration: none;
    color: var(--text-inverse);
}

/* === PREMIUM ALERTS === */
.alert {
    padding: var(--space-5);
    margin-bottom: var(--space-6);
    border: 2px solid transparent;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    gap: var(--space-4);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-danger {
    background: var(--danger-light);
    color: var(--danger);
    border-color: var(--danger-alpha);
}

.alert i {
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

/* === UTILITY CLASSES === */
.text-center { text-align: center; }
.text-right { text-align: right; }
.fw-semibold { font-weight: var(--font-weight-semibold); }
.fw-bold { font-weight: var(--font-weight-bold); }
.mb-4 { margin-bottom: var(--space-6); }
.me-2 { margin-right: var(--space-2); }
.p-0 { padding: 0 !important; }
.h-100 { height: 100%; }
.d-flex { display: flex; }
.align-items-center { align-items: center; }
.justify-content-center { justify-content: center; }

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-3) var(--space-5);
    border: 2px solid transparent;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-base);
    gap: var(--space-2);
    position: relative;
    overflow: hidden;
}

.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-xs);
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary);
    color: var(--text-inverse);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* === EMPTY STATES === */
.empty-state {
    text-align: center;
    padding: var(--space-12) var(--space-6);
    color: var(--text-tertiary);
}

.empty-state i {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--space-6);
    opacity: 0.5;
    color: var(--text-tertiary);
}

.empty-state-text {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    margin: 0;
    color: var(--text-secondary);
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1199.98px) {
    .col-lg-6 { flex: 0 0 100%; max-width: 100%; }
    .col-lg-4 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-8 { flex: 0 0 100%; max-width: 100%; }
}

@media (max-width: 991.98px) {
    .dashboard-content {
        padding: var(--space-4) var(--space-3);
    }

    .dashboard-header {
        padding: var(--space-5);
    }

    .dashboard-header-content {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-5);
    }

    .dashboard-title {
        font-size: var(--font-size-3xl);
    }

    .dashboard-actions {
        justify-content: center;
    }

    .search-container {
        min-width: 0;
        flex: 1;
    }

    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--space-5);
    }

    .col-lg-6, .col-lg-4, .col-lg-8 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .chart-container {
        height: 350px;
    }

    .chart-container-doughnut {
        height: 280px;
        max-width: 280px;
    }
}

@media (max-width: 767.98px) {
    .dashboard-content {
        padding: var(--space-3) var(--space-2);
    }

    .dashboard-header {
        padding: var(--space-4);
        margin-bottom: var(--space-6);
    }

    .dashboard-title {
        font-size: var(--font-size-2xl);
    }

    .dashboard-subtitle {
        font-size: var(--font-size-base);
    }

    .metrics-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-4);
    }

    .metric-card {
        padding: var(--space-5);
    }

    .metric-card-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-4);
    }

    .metric-icon {
        width: 56px;
        height: 56px;
        font-size: var(--font-size-xl);
    }

    .metric-value {
        font-size: var(--font-size-2xl);
    }

    .tab-controls {
        width: 100%;
        display: flex;
    }

    .tab-control {
        flex: 1;
        text-align: center;
    }

    .chart-container {
        height: 300px;
    }

    .chart-container-doughnut {
        height: 240px;
        max-width: 240px;
    }

    .data-table th,
    .data-table td {
        padding: var(--space-3) var(--space-4);
        font-size: var(--font-size-xs);
    }

    .avatar {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }

    .banner-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-5);
    }

    .banner-text {
        min-width: 0;
    }

    .banner-text h3 {
        font-size: var(--font-size-xl);
    }
}

@media (max-width: 575.98px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .metric-card-content {
        flex-direction: row;
        text-align: left;
        gap: var(--space-4);
    }

    .dashboard-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .search-container {
        width: 100%;
    }

    .action-buttons {
        justify-content: center;
    }
}

/* === LOADING STATES === */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    margin: -12px 0 0 -12px;
    border: 3px solid var(--border-primary);
    border-top-color: var(--primary);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* === FOCUS STATES === */
.btn-action:focus,
.search-input:focus,
.tab-control:focus,
.case-number:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* === PRINT STYLES === */
@media print {
    .dashboard-actions,
    .action-buttons,
    .btn,
    .btn-action {
        display: none !important;
    }

    .dashboard-content {
        padding: 0;
        max-width: none;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .metric-card {
        break-inside: avoid;
    }

    .chart-container,
    .chart-container-doughnut {
        height: auto;
        min-height: 200px;
    }
}
    --text-main-dark: var(--dark-dark);
    --text-muted-dark: var(--gray-dark);

    /* Default to Light Mode */
    --primary: var(--primary-light);
    --success: var(--success-light);
    --danger: var(--danger-light);
    --warning: var(--warning-light);
    --info: var(--info-light);
    --purple: var(--purple-light);
    --dark: var(--dark-light);
    --gray: var(--gray-light);
    --light-gray: var(--light-gray-light);
    --white: var(--white-light);
    --bg-main: var(--bg-main-light);
    --text-main: var(--text-main-light);
    --text-muted: var(--text-muted-light);
    --card-bg: var(--card-bg-light);
    --border-color: var(--border-light);
    --shadow-color: var(--shadow-color-light);

    /* Alpha Variants */
    --primary-light-alpha: hsla(var(--primary-hue), 90%, 58%, 0.1);
    --success-light-alpha: hsla(var(--success-hue), 53%, 48%, 0.1);
    --danger-light-alpha: hsla(var(--danger-hue), 80%, 54%, 0.1);
    --warning-light-alpha: hsla(var(--warning-hue), 96%, 50%, 0.1);
    --info-light-alpha: hsla(var(--info-hue), 56%, 52%, 0.1);
    --purple-light-alpha: hsla(var(--purple-hue), 88%, 64%, 0.1);

    --primary-dark-alpha: hsla(var(--primary-hue), 85%, 75%, 0.15);
    --success-dark-alpha: hsla(var(--success-hue), 45%, 65%, 0.15);
    --danger-dark-alpha: hsla(var(--danger-hue), 85%, 75%, 0.15);
    --warning-dark-alpha: hsla(var(--warning-hue), 95%, 70%, 0.15);
    --info-dark-alpha: hsla(var(--info-hue), 70%, 70%, 0.15);
    --purple-dark-alpha: hsla(var(--purple-hue), 85%, 75%, 0.15);

    --primary-alpha: var(--primary-light-alpha);
    --success-alpha: var(--success-light-alpha);
    --danger-alpha: var(--danger-light-alpha);
    --warning-alpha: var(--warning-light-alpha);
    --info-alpha: var(--info-light-alpha);
    --purple-alpha: var(--purple-light-alpha);

    /* Sizing & Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-xxl: 3rem;
    --border-radius-sm: 6px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;

    /* Shadows */
    --shadow-sm: 0 1px 2px hsla(var(--shadow-color), 0.05), 0 1px 3px hsla(var(--shadow-color), 0.1);
    --shadow-md: 0 4px 6px -1px hsla(var(--shadow-color), 0.1), 0 2px 4px -1px hsla(var(--shadow-color), 0.06);
    --shadow-lg: 0 10px 15px -3px hsla(var(--shadow-color), 0.1), 0 4px 6px -2px hsla(var(--shadow-color), 0.05);
    --shadow-xl: 0 20px 25px -5px hsla(var(--shadow-color), 0.1), 0 10px 10px -5px hsla(var(--shadow-color), 0.04);

    /* Transitions */
    --transition-fast: all 0.2s ease-in-out;
    --transition-base: all 0.3s ease-in-out;

    /* Font */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Dark Mode Theme */
[data-theme="dark"] {
    --primary: var(--primary-dark);
    --success: var(--success-dark);
    --danger: var(--danger-dark);
    --warning: var(--warning-dark);
    --info: var(--info-dark);
    --purple: var(--purple-dark);
    --dark: var(--dark-dark);
    --gray: var(--gray-dark);
    --light-gray: var(--light-gray-dark);
    --white: var(--white-dark);
    --bg-main: var(--bg-main-dark);
    --text-main: var(--text-main-dark);
    --text-muted: var(--text-muted-dark);
    --card-bg: var(--card-bg-dark);
    --border-color: var(--border-dark);
    --shadow-color: var(--shadow-color-dark);

    --primary-alpha: var(--primary-dark-alpha);
    --success-alpha: var(--success-dark-alpha);
    --danger-alpha: var(--danger-dark-alpha);
    --warning-alpha: var(--warning-dark-alpha);
    --info-alpha: var(--info-dark-alpha);
    --purple-alpha: var(--purple-dark-alpha);
}

/* Base Styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
}

body {
    background-color: var(--bg-main);
    font-family: var(--font-sans);
    color: var(--text-main);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color var(--transition-base), color var(--transition-base);
}

.page-container {
    padding: var(--space-lg) var(--space-xl);
    max-width: 1600px;
    margin: 0 auto;
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
    gap: var(--space-md);
    padding: var(--space-lg);
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.dashboard-title-wrapper {
    flex: 1;
    min-width: 200px;
}

.dashboard-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-main);
    margin: 0;
    line-height: 1.2;
}

.dashboard-subtitle {
    color: var(--text-muted);
    font-size: 1rem;
    margin-top: var(--space-xs);
    font-weight: 400;
}

.dashboard-actions {
    display: flex;
    gap: var(--space-md);
    align-items: center;
    flex-wrap: wrap;
}

/* Input Controls */
.input-group-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--bg-main);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: 0 var(--space-md);
    transition: var(--transition-fast);
    min-width: 250px;
}

.input-group-wrapper:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-alpha);
}

.input-group-wrapper i {
    color: var(--text-muted);
    margin-right: var(--space-sm);
    font-size: 1rem;
}

.input-control {
    border: none;
    background: transparent;
    padding: var(--space-md) 0;
    font-size: 0.9rem;
    color: var(--text-main);
    flex: 1;
    outline: none;
}

.input-control::placeholder {
    color: var(--text-muted);
}

.btn-action {
    background: var(--primary);
    color: white;
    border: none;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
}

.btn-action:hover {
    background: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-action i {
    font-size: 1rem;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.metric-card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
    border: 1px solid var(--border-color);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary);
    transition: var(--transition-base);
}

.metric-card.accent-primary::before { background: var(--primary); }
.metric-card.accent-success::before { background: var(--success); }
.metric-card.accent-danger::before { background: var(--danger); }
.metric-card.accent-warning::before { background: var(--warning); }
.metric-card.accent-info::before { background: var(--info); }
.metric-card.accent-purple::before { background: var(--purple); }

.metric-card-content {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.icon-primary { background: var(--primary); }
.icon-success { background: var(--success); }
.icon-danger { background: var(--danger); }
.icon-warning { background: var(--warning); }
.icon-info { background: var(--info); }
.icon-purple { background: var(--purple); }

.metric-content {
    flex: 1;
    min-width: 0;
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
    margin-bottom: var(--space-xs);
    display: block;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-main);
    line-height: 1.2;
    margin-bottom: var(--space-xs);
}

.metric-value .unit {
    font-size: 1rem;
    font-weight: 400;
    color: var(--text-muted);
    margin-left: var(--space-xs);
}

.metric-comparison {
    font-size: 0.8rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.trend-up {
    color: var(--success);
    font-weight: 600;
}

.trend-down {
    color: var(--danger);
    font-weight: 600;
}

/* Charts Section */
.charts-section {
    margin-bottom: var(--space-xl);
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -var(--space-sm);
}

.col-lg-6, .col-lg-4, .col-lg-8 {
    padding: 0 var(--space-sm);
    margin-bottom: var(--space-lg);
}

.col-lg-6 { flex: 0 0 50%; max-width: 50%; }
.col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }

.card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-base);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-main);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.card-title i {
    color: var(--primary);
    font-size: 1rem;
}

.card-body {
    padding: var(--space-lg);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.card-body.p-0 {
    padding: 0;
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 350px;
    width: 100%;
}

.chart-container-doughnut {
    position: relative;
    height: 300px;
    max-width: 300px;
    margin: 0 auto;
}

/* Tab Controls */
.tab-controls {
    display: flex;
    background: var(--bg-main);
    border-radius: var(--border-radius-md);
    padding: var(--space-xs);
    margin-bottom: var(--space-lg);
    border: 1px solid var(--border-color);
}

.tab-control {
    flex: 1;
    padding: var(--space-sm) var(--space-md);
    background: transparent;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-muted);
}

.tab-control.active {
    background: var(--primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.tab-control:hover:not(.active) {
    background: var(--primary-alpha);
    color: var(--primary);
}

/* Tables */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.data-table th,
.data-table td {
    padding: var(--space-md) var(--space-lg);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.data-table th {
    background: var(--bg-main);
    font-weight: 600;
    color: var(--text-main);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tbody tr {
    transition: var(--transition-fast);
}

.data-table tbody tr:hover {
    background: var(--primary-alpha);
}

.case-number {
    font-weight: 600;
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.case-number:hover {
    color: var(--primary);
    text-decoration: underline;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    gap: var(--space-xs);
}

.status-pending { background: var(--warning-alpha); color: var(--warning); }
.status-in-progress { background: var(--info-alpha); color: var(--info); }
.status-completed { background: var(--success-alpha); color: var(--success); }
.status-on-hold { background: var(--danger-alpha); color: var(--danger); }

.actions-group {
    display: flex;
    gap: var(--space-xs);
    align-items: center;
}

.actions-group .btn-action {
    padding: var(--space-xs);
    min-width: auto;
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
}

/* Dentist Info */
.dentist-info {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.avatar-A, .avatar-B, .avatar-C { background: var(--primary); }
.avatar-D, .avatar-E, .avatar-F { background: var(--success); }
.avatar-G, .avatar-H, .avatar-I { background: var(--info); }
.avatar-J, .avatar-K, .avatar-L { background: var(--warning); }
.avatar-M, .avatar-N, .avatar-O { background: var(--danger); }
.avatar-P, .avatar-Q, .avatar-R { background: var(--purple); }
.avatar-S, .avatar-T, .avatar-U { background: var(--primary); }
.avatar-V, .avatar-W, .avatar-X { background: var(--success); }
.avatar-Y, .avatar-Z { background: var(--info); }

.dentist-details {
    flex: 1;
    min-width: 0;
}

.dentist-name {
    font-weight: 600;
    color: var(--text-main);
    display: block;
    margin-bottom: var(--space-xs);
}

.dentist-clinic {
    font-size: 0.8rem;
    color: var(--text-muted);
    display: block;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--space-xxl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: var(--space-lg);
    opacity: 0.5;
}

.empty-state-text {
    font-size: 1rem;
    margin: 0;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.fw-semibold { font-weight: 600; }
.mb-4 { margin-bottom: var(--space-xl); }
.h-100 { height: 100%; }

/* Button Variants */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-sm) var(--space-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    gap: var(--space-xs);
}

.btn-sm {
    padding: var(--space-xs) var(--space-sm);
    font-size: 0.8rem;
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary);
    color: white;
}

/* Error State */
.error-state {
    text-align: center;
    padding: var(--space-xxl);
    background: var(--danger-alpha);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--danger);
    margin: var(--space-xl) 0;
}

.error-state h3 {
    color: var(--danger);
    margin-bottom: var(--space-md);
}

.error-state p {
    color: var(--text-muted);
    margin: 0;
}

/* Responsive Design */
@media (max-width: 1199.98px) {
    .col-lg-6 { flex: 0 0 100%; max-width: 100%; }
    .col-lg-4 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-8 { flex: 0 0 100%; max-width: 100%; }
}

@media (max-width: 991.98px) {
    .page-container {
        padding: var(--space-md) var(--space-lg);
    }

    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-lg);
        padding: var(--space-lg);
    }

    .dashboard-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .input-group-wrapper {
        min-width: 200px;
        flex: 1;
    }

    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-md);
    }

    .col-lg-6, .col-lg-4, .col-lg-8 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .chart-container {
        height: 300px;
    }

    .chart-container-doughnut {
        height: 250px;
        max-width: 250px;
    }
}

@media (max-width: 767.98px) {
    .page-container {
        padding: var(--space-sm) var(--space-md);
    }

    .dashboard-header {
        padding: var(--space-md);
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    .dashboard-subtitle {
        font-size: 0.9rem;
    }

    .metrics-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-sm);
    }

    .metric-card {
        padding: var(--space-md);
    }

    .metric-card-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }

    .metric-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .metric-value {
        font-size: 1.5rem;
    }

    .tab-controls {
        flex-direction: column;
        gap: var(--space-xs);
    }

    .tab-control {
        text-align: center;
    }

    .chart-container {
        height: 250px;
    }

    .chart-container-doughnut {
        height: 200px;
        max-width: 200px;
    }

    .data-table th,
    .data-table td {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.8rem;
    }

    .actions-group .btn-action {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .dentist-info {
        gap: var(--space-sm);
    }

    .avatar {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .input-group-wrapper {
        min-width: 0;
        width: 100%;
    }

    .dashboard-actions {
        gap: var(--space-sm);
    }
}

@media (max-width: 575.98px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .metric-card-content {
        flex-direction: row;
        text-align: left;
        gap: var(--space-lg);
    }

    .dashboard-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .input-group-wrapper {
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .dashboard-actions,
    .actions-group,
    .btn,
    .btn-action {
        display: none !important;
    }

    .page-container {
        padding: 0;
        max-width: none;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .metric-card {
        break-inside: avoid;
    }

    .chart-container,
    .chart-container-doughnut {
        height: auto;
        min-height: 200px;
    }
}

/* Focus and Accessibility */
.btn-action:focus,
.input-control:focus,
.tab-control:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

.case-number:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    border-radius: var(--border-radius-sm);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Ready to Ship Banner */
.ready-to-ship-banner {
    background: linear-gradient(105deg, var(--success-alpha) 0%, var(--card-bg) 70%);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    border-left: 5px solid var(--success);
}

.banner-content {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    flex-grow: 1;
}

.banner-icon {
    width: 60px;
    height: 60px;
    background: var(--card-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: var(--success);
    font-size: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.banner-text h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 var(--space-xs) 0;
    color: var(--text-main);
}

.banner-text p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9375rem;
}

.banner-text strong {
    color: var(--text-main);
    font-weight: 600;
}

.banner-action {
    margin-left: var(--space-md);
    margin-top: var(--space-sm);
}

.btn-ship {
    background: var(--success);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    box-shadow: var(--shadow-sm);
}

.btn-ship:hover {
    background: var(--success);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
    text-decoration: none;
}

/* Notifications */
.notification-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
    margin: 0;
    list-style: none;
}

.notification-item {
    display: flex;
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-fast);
    background-color: var(--card-bg);
    gap: var(--space-md);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item:hover {
    background-color: var(--primary-alpha);
}

.notification-important {
    border-left: 4px solid var(--primary);
    padding-left: calc(var(--space-lg) - 4px);
}

.notification-urgent {
    border-left: 4px solid var(--danger);
    padding-left: calc(var(--space-lg) - 4px);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--bg-main);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: var(--text-muted);
}

.notification-icon i {
    font-size: 1.2rem;
}

.notification-important .notification-icon {
    background-color: var(--primary-alpha);
    color: var(--primary);
}

.notification-urgent .notification-icon {
    background-color: var(--danger-alpha);
    color: var(--danger);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-main);
    margin-bottom: var(--space-xs);
    font-size: 0.9375rem;
}

.notification-text {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: var(--space-xs);
}

.notification-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    opacity: 0.8;
}

.notification-actions {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.notification-actions .btn {
    font-size: 0.8rem;
    padding: 0.3rem 0.7rem;
}

/* === PREMIUM ACTION GROUPS === */
.actions-group {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.actions-group .btn-action {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    border: 1px solid var(--border-primary);
    background: var(--surface);
    color: var(--text-secondary);
    transition: all var(--transition-base);
}

.actions-group .btn-action:hover {
    background: var(--primary);
    color: var(--text-inverse);
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Alert Styles */
.alert {
    padding: var(--space-md);
    margin-bottom: var(--space-lg);
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.alert-danger {
    color: var(--danger);
    background-color: var(--danger-alpha);
    border-color: var(--danger);
}

.alert i {
    font-size: 1.2em;
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-main: var(--bg-main-dark);
        --text-main: var(--text-main-dark);
        --text-muted: var(--text-muted-dark);
        --card-bg: var(--card-bg-dark);
        --border-color: var(--border-dark);
        --shadow-color: var(--shadow-color-dark);

        --primary: var(--primary-dark);
        --success: var(--success-dark);
        --danger: var(--danger-dark);
        --warning: var(--warning-dark);
        --info: var(--info-dark);
        --purple: var(--purple-dark);

        --primary-alpha: var(--primary-dark-alpha);
        --success-alpha: var(--success-dark-alpha);
        --danger-alpha: var(--danger-dark-alpha);
        --warning-alpha: var(--warning-dark-alpha);
        --info-alpha: var(--info-dark-alpha);
        --purple-alpha: var(--purple-dark-alpha);
    }
}
