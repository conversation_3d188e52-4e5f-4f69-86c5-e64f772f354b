/* Dashboard CSS - Modern Dental Lab Management System */

:root {
    /* Color Hues */
    --primary-hue: 211;
    --success-hue: 145;
    --danger-hue: 5;
    --warning-hue: 45;
    --info-hue: 185;
    --purple-hue: 270;

    /* Base Colors - Light Mode */
    --primary-light: hsl(var(--primary-hue), 90%, 58%);
    --success-light: hsl(var(--success-hue), 53%, 48%);
    --danger-light: hsl(var(--danger-hue), 80%, 54%);
    --warning-light: hsl(var(--warning-hue), 96%, 50%);
    --info-light: hsl(var(--info-hue), 56%, 52%);
    --purple-light: hsl(var(--purple-hue), 88%, 64%);
    --dark-light: hsl(210, 5%, 25%);
    --gray-light: hsl(210, 5%, 40%);
    --light-gray-light: hsl(210, 17%, 98%);
    --white-light: #ffffff;
    --border-light: hsla(0, 0%, 0%, 0.08);
    --shadow-color-light: 220, 3%, 15%;
    --bg-main-light: hsl(216, 33%, 97%);
    --card-bg-light: var(--white-light);
    --text-main-light: var(--dark-light);
    --text-muted-light: var(--gray-light);

    /* Base Colors - Dark Mode */
    --primary-dark: hsl(var(--primary-hue), 85%, 75%);
    --success-dark: hsl(var(--success-hue), 45%, 65%);
    --danger-dark: hsl(var(--danger-hue), 85%, 75%);
    --warning-dark: hsl(var(--warning-hue), 95%, 70%);
    --info-dark: hsl(var(--info-hue), 70%, 70%);
    --purple-dark: hsl(var(--purple-hue), 85%, 75%);
    --dark-dark: hsl(210, 15%, 90%);
    --gray-dark: hsl(210, 8%, 65%);
    --light-gray-dark: hsl(210, 5%, 25%);
    --white-dark: hsl(220, 4%, 13%);
    --border-dark: hsla(0, 0%, 100%, 0.08);
    --shadow-color-dark: 220, 40%, 2%;
    --bg-main-dark: var(--white-dark);
    --card-bg-dark: hsl(220, 3%, 16%);
    --text-main-dark: var(--dark-dark);
    --text-muted-dark: var(--gray-dark);

    /* Default to Light Mode */
    --primary: var(--primary-light);
    --success: var(--success-light);
    --danger: var(--danger-light);
    --warning: var(--warning-light);
    --info: var(--info-light);
    --purple: var(--purple-light);
    --dark: var(--dark-light);
    --gray: var(--gray-light);
    --light-gray: var(--light-gray-light);
    --white: var(--white-light);
    --bg-main: var(--bg-main-light);
    --text-main: var(--text-main-light);
    --text-muted: var(--text-muted-light);
    --card-bg: var(--card-bg-light);
    --border-color: var(--border-light);
    --shadow-color: var(--shadow-color-light);

    /* Alpha Variants */
    --primary-light-alpha: hsla(var(--primary-hue), 90%, 58%, 0.1);
    --success-light-alpha: hsla(var(--success-hue), 53%, 48%, 0.1);
    --danger-light-alpha: hsla(var(--danger-hue), 80%, 54%, 0.1);
    --warning-light-alpha: hsla(var(--warning-hue), 96%, 50%, 0.1);
    --info-light-alpha: hsla(var(--info-hue), 56%, 52%, 0.1);
    --purple-light-alpha: hsla(var(--purple-hue), 88%, 64%, 0.1);

    --primary-dark-alpha: hsla(var(--primary-hue), 85%, 75%, 0.15);
    --success-dark-alpha: hsla(var(--success-hue), 45%, 65%, 0.15);
    --danger-dark-alpha: hsla(var(--danger-hue), 85%, 75%, 0.15);
    --warning-dark-alpha: hsla(var(--warning-hue), 95%, 70%, 0.15);
    --info-dark-alpha: hsla(var(--info-hue), 70%, 70%, 0.15);
    --purple-dark-alpha: hsla(var(--purple-hue), 85%, 75%, 0.15);

    --primary-alpha: var(--primary-light-alpha);
    --success-alpha: var(--success-light-alpha);
    --danger-alpha: var(--danger-light-alpha);
    --warning-alpha: var(--warning-light-alpha);
    --info-alpha: var(--info-light-alpha);
    --purple-alpha: var(--purple-light-alpha);

    /* Sizing & Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-xxl: 3rem;
    --border-radius-sm: 6px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;

    /* Shadows */
    --shadow-sm: 0 1px 2px hsla(var(--shadow-color), 0.05), 0 1px 3px hsla(var(--shadow-color), 0.1);
    --shadow-md: 0 4px 6px -1px hsla(var(--shadow-color), 0.1), 0 2px 4px -1px hsla(var(--shadow-color), 0.06);
    --shadow-lg: 0 10px 15px -3px hsla(var(--shadow-color), 0.1), 0 4px 6px -2px hsla(var(--shadow-color), 0.05);
    --shadow-xl: 0 20px 25px -5px hsla(var(--shadow-color), 0.1), 0 10px 10px -5px hsla(var(--shadow-color), 0.04);

    /* Transitions */
    --transition-fast: all 0.2s ease-in-out;
    --transition-base: all 0.3s ease-in-out;

    /* Font */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Dark Mode Theme */
[data-theme="dark"] {
    --primary: var(--primary-dark);
    --success: var(--success-dark);
    --danger: var(--danger-dark);
    --warning: var(--warning-dark);
    --info: var(--info-dark);
    --purple: var(--purple-dark);
    --dark: var(--dark-dark);
    --gray: var(--gray-dark);
    --light-gray: var(--light-gray-dark);
    --white: var(--white-dark);
    --bg-main: var(--bg-main-dark);
    --text-main: var(--text-main-dark);
    --text-muted: var(--text-muted-dark);
    --card-bg: var(--card-bg-dark);
    --border-color: var(--border-dark);
    --shadow-color: var(--shadow-color-dark);

    --primary-alpha: var(--primary-dark-alpha);
    --success-alpha: var(--success-dark-alpha);
    --danger-alpha: var(--danger-dark-alpha);
    --warning-alpha: var(--warning-dark-alpha);
    --info-alpha: var(--info-dark-alpha);
    --purple-alpha: var(--purple-dark-alpha);
}

/* Base Styles */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
}

body {
    background-color: var(--bg-main);
    font-family: var(--font-sans);
    color: var(--text-main);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    transition: background-color var(--transition-base), color var(--transition-base);
}

.page-container {
    padding: var(--space-lg) var(--space-xl);
    max-width: 1600px;
    margin: 0 auto;
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-xl);
    gap: var(--space-md);
    padding: var(--space-lg);
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.dashboard-title-wrapper {
    flex: 1;
    min-width: 200px;
}

.dashboard-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-main);
    margin: 0;
    line-height: 1.2;
}

.dashboard-subtitle {
    color: var(--text-muted);
    font-size: 1rem;
    margin-top: var(--space-xs);
    font-weight: 400;
}

.dashboard-actions {
    display: flex;
    gap: var(--space-md);
    align-items: center;
    flex-wrap: wrap;
}

/* Input Controls */
.input-group-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--bg-main);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: 0 var(--space-md);
    transition: var(--transition-fast);
    min-width: 250px;
}

.input-group-wrapper:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-alpha);
}

.input-group-wrapper i {
    color: var(--text-muted);
    margin-right: var(--space-sm);
    font-size: 1rem;
}

.input-control {
    border: none;
    background: transparent;
    padding: var(--space-md) 0;
    font-size: 0.9rem;
    color: var(--text-main);
    flex: 1;
    outline: none;
}

.input-control::placeholder {
    color: var(--text-muted);
}

.btn-action {
    background: var(--primary);
    color: white;
    border: none;
    padding: var(--space-sm) var(--space-md);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
}

.btn-action:hover {
    background: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-action i {
    font-size: 1rem;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-lg);
    margin-bottom: var(--space-xl);
}

.metric-card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
    border: 1px solid var(--border-color);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary);
    transition: var(--transition-base);
}

.metric-card.accent-primary::before { background: var(--primary); }
.metric-card.accent-success::before { background: var(--success); }
.metric-card.accent-danger::before { background: var(--danger); }
.metric-card.accent-warning::before { background: var(--warning); }
.metric-card.accent-info::before { background: var(--info); }
.metric-card.accent-purple::before { background: var(--purple); }

.metric-card-content {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.icon-primary { background: var(--primary); }
.icon-success { background: var(--success); }
.icon-danger { background: var(--danger); }
.icon-warning { background: var(--warning); }
.icon-info { background: var(--info); }
.icon-purple { background: var(--purple); }

.metric-content {
    flex: 1;
    min-width: 0;
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
    margin-bottom: var(--space-xs);
    display: block;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-main);
    line-height: 1.2;
    margin-bottom: var(--space-xs);
}

.metric-value .unit {
    font-size: 1rem;
    font-weight: 400;
    color: var(--text-muted);
    margin-left: var(--space-xs);
}

.metric-comparison {
    font-size: 0.8rem;
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.trend-up {
    color: var(--success);
    font-weight: 600;
}

.trend-down {
    color: var(--danger);
    font-weight: 600;
}

/* Charts Section */
.charts-section {
    margin-bottom: var(--space-xl);
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -var(--space-sm);
}

.col-lg-6, .col-lg-4, .col-lg-8 {
    padding: 0 var(--space-sm);
    margin-bottom: var(--space-lg);
}

.col-lg-6 { flex: 0 0 50%; max-width: 50%; }
.col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }

.card {
    background: var(--card-bg);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-base);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-main);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.card-title i {
    color: var(--primary);
    font-size: 1rem;
}

.card-body {
    padding: var(--space-lg);
    flex: 1;
    display: flex;
    flex-direction: column;
}

.card-body.p-0 {
    padding: 0;
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 350px;
    width: 100%;
}

.chart-container-doughnut {
    position: relative;
    height: 300px;
    max-width: 300px;
    margin: 0 auto;
}

/* Tab Controls */
.tab-controls {
    display: flex;
    background: var(--bg-main);
    border-radius: var(--border-radius-md);
    padding: var(--space-xs);
    margin-bottom: var(--space-lg);
    border: 1px solid var(--border-color);
}

.tab-control {
    flex: 1;
    padding: var(--space-sm) var(--space-md);
    background: transparent;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-muted);
}

.tab-control.active {
    background: var(--primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.tab-control:hover:not(.active) {
    background: var(--primary-alpha);
    color: var(--primary);
}

/* Tables */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.data-table th,
.data-table td {
    padding: var(--space-md) var(--space-lg);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.data-table th {
    background: var(--bg-main);
    font-weight: 600;
    color: var(--text-main);
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table tbody tr {
    transition: var(--transition-fast);
}

.data-table tbody tr:hover {
    background: var(--primary-alpha);
}

.case-number {
    font-weight: 600;
    color: var(--primary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.case-number:hover {
    color: var(--primary);
    text-decoration: underline;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    gap: var(--space-xs);
}

.status-pending { background: var(--warning-alpha); color: var(--warning); }
.status-in-progress { background: var(--info-alpha); color: var(--info); }
.status-completed { background: var(--success-alpha); color: var(--success); }
.status-on-hold { background: var(--danger-alpha); color: var(--danger); }

.actions-group {
    display: flex;
    gap: var(--space-xs);
    align-items: center;
}

.actions-group .btn-action {
    padding: var(--space-xs);
    min-width: auto;
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
}

/* Dentist Info */
.dentist-info {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.avatar-A, .avatar-B, .avatar-C { background: var(--primary); }
.avatar-D, .avatar-E, .avatar-F { background: var(--success); }
.avatar-G, .avatar-H, .avatar-I { background: var(--info); }
.avatar-J, .avatar-K, .avatar-L { background: var(--warning); }
.avatar-M, .avatar-N, .avatar-O { background: var(--danger); }
.avatar-P, .avatar-Q, .avatar-R { background: var(--purple); }
.avatar-S, .avatar-T, .avatar-U { background: var(--primary); }
.avatar-V, .avatar-W, .avatar-X { background: var(--success); }
.avatar-Y, .avatar-Z { background: var(--info); }

.dentist-details {
    flex: 1;
    min-width: 0;
}

.dentist-name {
    font-weight: 600;
    color: var(--text-main);
    display: block;
    margin-bottom: var(--space-xs);
}

.dentist-clinic {
    font-size: 0.8rem;
    color: var(--text-muted);
    display: block;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--space-xxl);
    color: var(--text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: var(--space-lg);
    opacity: 0.5;
}

.empty-state-text {
    font-size: 1rem;
    margin: 0;
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.fw-semibold { font-weight: 600; }
.mb-4 { margin-bottom: var(--space-xl); }
.h-100 { height: 100%; }

/* Button Variants */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-sm) var(--space-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    gap: var(--space-xs);
}

.btn-sm {
    padding: var(--space-xs) var(--space-sm);
    font-size: 0.8rem;
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--primary);
    color: white;
}

/* Error State */
.error-state {
    text-align: center;
    padding: var(--space-xxl);
    background: var(--danger-alpha);
    border-radius: var(--border-radius-lg);
    border: 1px solid var(--danger);
    margin: var(--space-xl) 0;
}

.error-state h3 {
    color: var(--danger);
    margin-bottom: var(--space-md);
}

.error-state p {
    color: var(--text-muted);
    margin: 0;
}

/* Responsive Design */
@media (max-width: 1199.98px) {
    .col-lg-6 { flex: 0 0 100%; max-width: 100%; }
    .col-lg-4 { flex: 0 0 50%; max-width: 50%; }
    .col-lg-8 { flex: 0 0 100%; max-width: 100%; }
}

@media (max-width: 991.98px) {
    .page-container {
        padding: var(--space-md) var(--space-lg);
    }

    .dashboard-header {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-lg);
        padding: var(--space-lg);
    }

    .dashboard-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .input-group-wrapper {
        min-width: 200px;
        flex: 1;
    }

    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--space-md);
    }

    .col-lg-6, .col-lg-4, .col-lg-8 {
        flex: 0 0 100%;
        max-width: 100%;
    }

    .chart-container {
        height: 300px;
    }

    .chart-container-doughnut {
        height: 250px;
        max-width: 250px;
    }
}

@media (max-width: 767.98px) {
    .page-container {
        padding: var(--space-sm) var(--space-md);
    }

    .dashboard-header {
        padding: var(--space-md);
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    .dashboard-subtitle {
        font-size: 0.9rem;
    }

    .metrics-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--space-sm);
    }

    .metric-card {
        padding: var(--space-md);
    }

    .metric-card-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-md);
    }

    .metric-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    .metric-value {
        font-size: 1.5rem;
    }

    .tab-controls {
        flex-direction: column;
        gap: var(--space-xs);
    }

    .tab-control {
        text-align: center;
    }

    .chart-container {
        height: 250px;
    }

    .chart-container-doughnut {
        height: 200px;
        max-width: 200px;
    }

    .data-table th,
    .data-table td {
        padding: var(--space-sm) var(--space-md);
        font-size: 0.8rem;
    }

    .actions-group .btn-action {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .dentist-info {
        gap: var(--space-sm);
    }

    .avatar {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .input-group-wrapper {
        min-width: 0;
        width: 100%;
    }

    .dashboard-actions {
        gap: var(--space-sm);
    }
}

@media (max-width: 575.98px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .metric-card-content {
        flex-direction: row;
        text-align: left;
        gap: var(--space-lg);
    }

    .dashboard-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .input-group-wrapper {
        width: 100%;
    }
}

/* Print Styles */
@media print {
    .dashboard-actions,
    .actions-group,
    .btn,
    .btn-action {
        display: none !important;
    }

    .page-container {
        padding: 0;
        max-width: none;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .metric-card {
        break-inside: avoid;
    }

    .chart-container,
    .chart-container-doughnut {
        height: auto;
        min-height: 200px;
    }
}

/* Focus and Accessibility */
.btn-action:focus,
.input-control:focus,
.tab-control:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

.case-number:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
    border-radius: var(--border-radius-sm);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top-color: var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Ready to Ship Banner */
.ready-to-ship-banner {
    background: linear-gradient(105deg, var(--success-alpha) 0%, var(--card-bg) 70%);
    border-radius: var(--border-radius-lg);
    padding: var(--space-lg);
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    border-left: 5px solid var(--success);
}

.banner-content {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
    flex-grow: 1;
}

.banner-icon {
    width: 60px;
    height: 60px;
    background: var(--card-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: var(--success);
    font-size: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.banner-text h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 var(--space-xs) 0;
    color: var(--text-main);
}

.banner-text p {
    margin: 0;
    color: var(--text-muted);
    font-size: 0.9375rem;
}

.banner-text strong {
    color: var(--text-main);
    font-weight: 600;
}

.banner-action {
    margin-left: var(--space-md);
    margin-top: var(--space-sm);
}

.btn-ship {
    background: var(--success);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    box-shadow: var(--shadow-sm);
}

.btn-ship:hover {
    background: var(--success);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
    text-decoration: none;
}

/* Notifications */
.notification-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
    margin: 0;
    list-style: none;
}

.notification-item {
    display: flex;
    padding: var(--space-md) var(--space-lg);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-fast);
    background-color: var(--card-bg);
    gap: var(--space-md);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-item:hover {
    background-color: var(--primary-alpha);
}

.notification-important {
    border-left: 4px solid var(--primary);
    padding-left: calc(var(--space-lg) - 4px);
}

.notification-urgent {
    border-left: 4px solid var(--danger);
    padding-left: calc(var(--space-lg) - 4px);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--bg-main);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    color: var(--text-muted);
}

.notification-icon i {
    font-size: 1.2rem;
}

.notification-important .notification-icon {
    background-color: var(--primary-alpha);
    color: var(--primary);
}

.notification-urgent .notification-icon {
    background-color: var(--danger-alpha);
    color: var(--danger);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-main);
    margin-bottom: var(--space-xs);
    font-size: 0.9375rem;
}

.notification-text {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: var(--space-xs);
}

.notification-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    opacity: 0.8;
}

.notification-actions {
    display: flex;
    align-items: center;
    margin-left: auto;
}

.notification-actions .btn {
    font-size: 0.8rem;
    padding: 0.3rem 0.7rem;
}

/* Alert Styles */
.alert {
    padding: var(--space-md);
    margin-bottom: var(--space-lg);
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.alert-danger {
    color: var(--danger);
    background-color: var(--danger-alpha);
    border-color: var(--danger);
}

.alert i {
    font-size: 1.2em;
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-main: var(--bg-main-dark);
        --text-main: var(--text-main-dark);
        --text-muted: var(--text-muted-dark);
        --card-bg: var(--card-bg-dark);
        --border-color: var(--border-dark);
        --shadow-color: var(--shadow-color-dark);

        --primary: var(--primary-dark);
        --success: var(--success-dark);
        --danger: var(--danger-dark);
        --warning: var(--warning-dark);
        --info: var(--info-dark);
        --purple: var(--purple-dark);

        --primary-alpha: var(--primary-dark-alpha);
        --success-alpha: var(--success-dark-alpha);
        --danger-alpha: var(--danger-dark-alpha);
        --warning-alpha: var(--warning-dark-alpha);
        --info-alpha: var(--info-dark-alpha);
        --purple-alpha: var(--purple-dark-alpha);
    }
}
