{% extends 'base.html' %}
{% load static %}
{% load humanize %}
{% load common_filters %}

{% block title %}Workflow Efficiency Analysis{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
<style>
    .efficiency-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    .efficiency-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .efficiency-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .bottleneck-item {
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 8px;
        border-left: 4px solid #dc3545;
        background-color: #f8d7da;
    }
    .department-item {
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 8px;
        border-left: 4px solid;
    }
    .dept-excellent {
        background-color: #d4edda;
        border-left-color: #28a745;
    }
    .dept-good {
        background-color: #d1ecf1;
        border-left-color: #17a2b8;
    }
    .dept-needs-improvement {
        background-color: #f8d7da;
        border-left-color: #dc3545;
    }
    .stage-performance {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-cogs"></i> Workflow Efficiency Analysis
            </h1>

            <!-- Date Range Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date"
                                   value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date"
                                   value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="fas fa-filter"></i> Apply Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Efficiency KPIs -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="efficiency-card">
                <div class="efficiency-value">{{ total_cases }}</div>
                <div class="efficiency-label">Total Cases</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="efficiency-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="efficiency-value">{{ completed_cases }}</div>
                <div class="efficiency-label">Completed Cases</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="efficiency-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="efficiency-value">{{ utilization_rate|floatformat:1 }}%</div>
                <div class="efficiency-label">Resource Utilization</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="efficiency-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="efficiency-value">{{ stage_performance|length }}</div>
                <div class="efficiency-label">Active Stages</div>
            </div>
        </div>
    </div>

    <!-- Bottlenecks and Department Performance -->
    <div class="row">
        <!-- Bottleneck Identification -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle"></i> Identified Bottlenecks</h5>
                </div>
                <div class="card-body">
                    {% for bottleneck in bottlenecks %}
                    <div class="bottleneck-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ bottleneck.current_stage__name|default:"Unknown Stage" }}</h6>
                                <small>{{ bottleneck.case_count }} cases affected</small>
                            </div>
                            <div class="text-end">
                                <strong>{{ bottleneck.avg_hours|floatformat:1 }}h</strong><br>
                                <small>avg time</small>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> No significant bottlenecks identified!
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Department Efficiency -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-building"></i> Department Efficiency</h5>
                </div>
                <div class="card-body">
                    {% for dept in department_efficiency %}
                    <div class="department-item
                        {% if dept.efficiency_score >= 80 %}dept-excellent
                        {% elif dept.efficiency_score >= 60 %}dept-good
                        {% else %}dept-needs-improvement{% endif %}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ dept.department }}</h6>
                                <small>{{ dept.completed_cases }}/{{ dept.total_cases }} completed</small>
                            </div>
                            <div class="text-end">
                                <strong>{{ dept.efficiency_score|floatformat:1 }}</strong><br>
                                <small>efficiency score</small>
                            </div>
                        </div>
                        <div class="mt-2">
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar
                                    {% if dept.efficiency_score >= 80 %}bg-success
                                    {% elif dept.efficiency_score >= 60 %}bg-warning
                                    {% else %}bg-danger{% endif %}"
                                    style="width: {{ dept.efficiency_score }}%"></div>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">No department data available.</p>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Stage Performance Analysis -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-tasks"></i> Stage Performance Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for stage in stage_performance %}
                        <div class="col-lg-4 col-md-6 mb-3">
                            <div class="stage-performance">
                                <h6>{{ stage.current_stage__name|default:"Unknown Stage" }}</h6>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Cases:</span>
                                    <strong>{{ stage.case_count }}</strong>
                                </div>
                                <div class="d-flex justify-content-between mb-2">
                                    <span>Avg Time:</span>
                                    <strong>{{ stage.avg_hours|floatformat:1 }}h</strong>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar
                                        {% if stage.avg_hours <= 24 %}bg-success
                                        {% elif stage.avg_hours <= 72 %}bg-warning
                                        {% else %}bg-danger{% endif %}"
                                        style="width: {{ stage.case_count|multiply:10|divide:total_cases|default:0 }}%"></div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="col-12">
                            <p class="text-muted text-center">No stage performance data available.</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Efficiency Trends -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Weekly Efficiency Trends</h5>
                </div>
                <div class="card-body">
                    <canvas id="efficiencyTrendsChart" height="100"></canvas>
                </div>
            </div>
        </div>

        <!-- Recommendations -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> Improvement Recommendations</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Quick Wins</h6>
                        <ul class="mb-0">
                            {% if bottlenecks %}
                            <li>Address bottleneck in {{ bottlenecks.0.current_stage__name|default:"identified stage" }}</li>
                            {% endif %}
                            {% if utilization_rate < 70 %}
                            <li>Improve resource utilization (currently {{ utilization_rate|floatformat:1 }}%)</li>
                            {% endif %}
                            <li>Standardize processes across departments</li>
                            <li>Implement workflow automation</li>
                        </ul>
                    </div>

                    {% if utilization_rate < 60 %}
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Action Required</h6>
                        <p class="mb-0">Resource utilization is below 60%. Consider capacity optimization.</p>
                    </div>
                    {% endif %}

                    <div class="alert alert-success">
                        <h6><i class="fas fa-target"></i> Efficiency Targets</h6>
                        <ul class="mb-0">
                            <li>Completion Rate: >90%</li>
                            <li>Utilization Rate: >75%</li>
                            <li>Avg Cycle Time: <72h</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Performance Table -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> Detailed Performance Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Department</th>
                                    <th>Total Cases</th>
                                    <th>Completed</th>
                                    <th>Completion Rate</th>
                                    <th>Avg Cycle Time</th>
                                    <th>Efficiency Score</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dept in department_efficiency %}
                                <tr>
                                    <td><strong>{{ dept.department }}</strong></td>
                                    <td>{{ dept.total_cases }}</td>
                                    <td>{{ dept.completed_cases }}</td>
                                    <td>
                                        <span class="badge
                                            {% if dept.completion_rate >= 90 %}bg-success
                                            {% elif dept.completion_rate >= 70 %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ dept.completion_rate|floatformat:1 }}%
                                        </span>
                                    </td>
                                    <td>{{ dept.avg_cycle_hours|floatformat:1 }}h</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="progress me-2" style="width: 60px; height: 8px;">
                                                <div class="progress-bar
                                                    {% if dept.efficiency_score >= 80 %}bg-success
                                                    {% elif dept.efficiency_score >= 60 %}bg-warning
                                                    {% else %}bg-danger{% endif %}"
                                                    style="width: {{ dept.efficiency_score }}%"></div>
                                            </div>
                                            <small>{{ dept.efficiency_score|floatformat:1 }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if dept.efficiency_score >= 80 %}
                                        <span class="badge bg-success">Excellent</span>
                                        {% elif dept.efficiency_score >= 60 %}
                                        <span class="badge bg-warning">Good</span>
                                        {% else %}
                                        <span class="badge bg-danger">Needs Improvement</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center text-muted">No department data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script>
// Efficiency Trends Chart
const efficiencyTrendsCtx = document.getElementById('efficiencyTrendsChart').getContext('2d');
const efficiencyTrendsChart = new Chart(efficiencyTrendsCtx, {
    type: 'line',
    data: {
        labels: [
            {% for week in weekly_efficiency %}
            'Week {{ forloop.counter }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Completion Rate (%)',
            data: [
                {% for week in weekly_efficiency %}
                {{ week.completion_rate|default:0 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                title: {
                    display: true,
                    text: 'Completion Rate (%)'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'Time Period'
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>
{% endblock %}
