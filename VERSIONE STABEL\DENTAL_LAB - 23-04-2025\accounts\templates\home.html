{% extends "base.html" %}
{% load static humanize mathfilters %}

{% block title %}Dashboard | Dental Case Management{% endblock %}

{% block extra_css %}
<!-- Bootstrap Icons -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">

<!-- Premium Dashboard CSS -->
<link rel="stylesheet" href="{% static 'css/dashboard.css' %}" type="text/css">

<!-- Debug: CSS Path -->
<!-- CSS Path: {% static 'css/dashboard.css' %} -->

<!-- PREMIUM DASHBOARD CSS - EMBEDDED FOR IMMEDIATE STYLING -->
<style>
/* === PREMIUM DESIGN SYSTEM === */
:root {
    /* Primary Brand Colors */
    --dental-blue: #2563eb;
    --dental-blue-light: #3b82f6;
    --dental-blue-dark: #1d4ed8;
    --dental-blue-50: #eff6ff;
    --dental-blue-100: #dbeafe;
    --dental-blue-500: #3b82f6;
    --dental-blue-600: #2563eb;
    --dental-blue-700: #1d4ed8;
    --dental-blue-900: #1e3a8a;

    /* Success Colors */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;

    /* Warning Colors */
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-500: #f59e0b;
    --warning-600: #d97706;

    /* Danger Colors */
    --danger-50: #fef2f2;
    --danger-100: #fee2e2;
    --danger-500: #ef4444;
    --danger-600: #dc2626;

    /* Info Colors */
    --info-50: #f0f9ff;
    --info-100: #e0f2fe;
    --info-500: #06b6d4;
    --info-600: #0891b2;

    /* Purple Colors */
    --purple-50: #faf5ff;
    --purple-100: #f3e8ff;
    --purple-500: #a855f7;
    --purple-600: #9333ea;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Semantic Colors */
    --primary: var(--dental-blue-600);
    --primary-hover: var(--dental-blue-700);
    --primary-light: var(--dental-blue-50);
    --primary-alpha: rgba(37, 99, 235, 0.1);

    --success: var(--success-600);
    --success-light: var(--success-50);
    --success-alpha: rgba(34, 197, 94, 0.1);

    --warning: var(--warning-600);
    --warning-light: var(--warning-50);
    --warning-alpha: rgba(245, 158, 11, 0.1);

    --danger: var(--danger-600);
    --danger-light: var(--danger-50);
    --danger-alpha: rgba(239, 68, 68, 0.1);

    --info: var(--info-600);
    --info-light: var(--info-50);
    --info-alpha: rgba(6, 182, 212, 0.1);

    --purple: var(--purple-600);
    --purple-light: var(--purple-50);
    --purple-alpha: rgba(168, 85, 247, 0.1);

    /* Background & Surface */
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);
    --surface: #ffffff;
    --surface-elevated: #ffffff;

    /* Text Colors */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-tertiary: var(--gray-500);
    --text-inverse: #ffffff;

    /* Borders */
    --border-primary: var(--gray-200);
    --border-secondary: var(--gray-100);
    --border-focus: var(--dental-blue-500);

    /* Shadows */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Spacing System */
    --space-0: 0;
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;

    /* Border Radius */
    --radius-none: 0;
    --radius-sm: 0.125rem;
    --radius-base: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;
    --radius-3xl: 1.5rem;
    --radius-full: 9999px;

    /* Typography */
    --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;

    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Dark Mode Theme */
[data-theme="dark"] {
    --bg-primary: var(--gray-900);
    --bg-secondary: var(--gray-800);
    --bg-tertiary: var(--gray-700);
    --surface: var(--gray-800);
    --surface-elevated: var(--gray-700);

    --text-primary: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-400);

    --border-primary: var(--gray-700);
    --border-secondary: var(--gray-800);

    --primary-alpha: rgba(59, 130, 246, 0.15);
    --success-alpha: rgba(34, 197, 94, 0.15);
    --warning-alpha: rgba(245, 158, 11, 0.15);
    --danger-alpha: rgba(239, 68, 68, 0.15);
    --info-alpha: rgba(6, 182, 212, 0.15);
    --purple-alpha: rgba(168, 85, 247, 0.15);
}

/* Global Reset & Base Styles */
*,
*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-sans);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    transition: background-color var(--transition-base), color var(--transition-base);
}

/* Layout Containers */
.dashboard-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.dashboard-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--space-6) var(--space-4);
}

/* === PREMIUM DASHBOARD HEADER === */
.dashboard-header {
    background: var(--surface);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    margin-bottom: var(--space-8);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--info) 50%, var(--purple) 100%);
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.dashboard-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--space-6);
}

.dashboard-title-section {
    flex: 1;
    min-width: 300px;
}

.dashboard-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-extrabold);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    background: linear-gradient(135deg, var(--primary) 0%, var(--purple) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: var(--line-height-tight);
}

.dashboard-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.dashboard-subtitle i {
    color: var(--primary);
    font-size: var(--font-size-base);
}

.dashboard-actions {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

/* === PREMIUM SEARCH INPUT === */
.search-container {
    position: relative;
    min-width: 280px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: var(--bg-secondary);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-3) var(--space-4);
    transition: all var(--transition-base);
    backdrop-filter: blur(10px);
}

.search-input-wrapper:focus-within {
    border-color: var(--primary);
    box-shadow: 0 0 0 4px var(--primary-alpha);
    transform: translateY(-1px);
}

.search-input-wrapper i {
    color: var(--text-tertiary);
    margin-right: var(--space-3);
    font-size: var(--font-size-lg);
    transition: color var(--transition-base);
}

.search-input-wrapper:focus-within i {
    color: var(--primary);
}

.search-input {
    flex: 1;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    outline: none;
    padding: var(--space-1) 0;
}

.search-input::placeholder {
    color: var(--text-tertiary);
    font-weight: var(--font-weight-normal);
}

/* Select dropdown styling */
.search-input select,
select.search-input {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
    cursor: pointer;
}

/* === PREMIUM ACTION BUTTONS === */
.action-buttons {
    display: flex;
    gap: var(--space-3);
}

.btn-action {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-xl);
    border: 2px solid var(--border-primary);
    background: var(--surface);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn-action:hover::before {
    left: 100%;
}

.btn-action:hover {
    border-color: var(--primary);
    color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-action:active {
    transform: translateY(0);
}

.btn-action.btn-primary {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-hover) 100%);
    border-color: var(--primary);
    color: var(--text-inverse);
}

.btn-action.btn-primary:hover {
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
    transform: translateY(-3px);
}

/* === PREMIUM METRICS GRID === */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-10);
}

.metric-card {
    background: var(--surface);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--info) 100%);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform var(--transition-base);
}

.metric-card:hover::before {
    transform: scaleX(1);
}

.metric-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-alpha);
}

.metric-card.accent-primary::before {
    background: linear-gradient(90deg, var(--primary) 0%, var(--dental-blue-light) 100%);
}

.metric-card.accent-success::before {
    background: linear-gradient(90deg, var(--success) 0%, var(--success-500) 100%);
}

.metric-card.accent-warning::before {
    background: linear-gradient(90deg, var(--warning) 0%, var(--warning-500) 100%);
}

.metric-card.accent-danger::before {
    background: linear-gradient(90deg, var(--danger) 0%, var(--danger-500) 100%);
}

.metric-card.accent-info::before {
    background: linear-gradient(90deg, var(--info) 0%, var(--info-500) 100%);
}

.metric-card.accent-purple::before {
    background: linear-gradient(90deg, var(--purple) 0%, var(--purple-500) 100%);
}

.metric-card-content {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.metric-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.metric-icon::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: inherit;
}

.metric-icon i {
    position: relative;
    z-index: 1;
}

.icon-primary {
    background: linear-gradient(135deg, var(--primary-alpha) 0%, var(--primary-light) 100%);
    color: var(--primary);
    border: 1px solid var(--primary-alpha);
}

.icon-success {
    background: linear-gradient(135deg, var(--success-alpha) 0%, var(--success-light) 100%);
    color: var(--success);
    border: 1px solid var(--success-alpha);
}

.icon-warning {
    background: linear-gradient(135deg, var(--warning-alpha) 0%, var(--warning-light) 100%);
    color: var(--warning);
    border: 1px solid var(--warning-alpha);
}

.icon-danger {
    background: linear-gradient(135deg, var(--danger-alpha) 0%, var(--danger-light) 100%);
    color: var(--danger);
    border: 1px solid var(--danger-alpha);
}

.icon-info {
    background: linear-gradient(135deg, var(--info-alpha) 0%, var(--info-light) 100%);
    color: var(--info);
    border: 1px solid var(--info-alpha);
}

.icon-purple {
    background: linear-gradient(135deg, var(--purple-alpha) 0%, var(--purple-light) 100%);
    color: var(--purple);
    border: 1px solid var(--purple-alpha);
}

.metric-content {
    flex: 1;
}

.metric-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin-bottom: var(--space-1);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.metric-value {
    font-size: var(--font-size-3xl);
    font-weight: var(--font-weight-extrabold);
    color: var(--text-primary);
    line-height: var(--line-height-tight);
    margin-bottom: var(--space-2);
}

.metric-value .unit {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
    margin-left: var(--space-1);
}

.metric-comparison {
    font-size: var(--font-size-sm);
    color: var(--text-tertiary);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.trend-up {
    color: var(--success);
    font-weight: var(--font-weight-semibold);
}

.trend-down {
    color: var(--danger);
    font-weight: var(--font-weight-semibold);
}

.trend-neutral {
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

/* === PREMIUM READY TO SHIP BANNER === */
.ready-to-ship-banner {
    background: linear-gradient(135deg, var(--success-alpha) 0%, var(--success-light) 100%);
    border: 1px solid var(--success-alpha);
    border-left: 4px solid var(--success);
    border-radius: var(--radius-2xl);
    padding: var(--space-6);
    margin-bottom: var(--space-8);
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--space-6);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.ready-to-ship-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, transparent 100%);
    pointer-events: none;
}

.banner-content {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    flex: 1;
}

.banner-icon {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-xl);
    background: linear-gradient(135deg, var(--success) 0%, var(--success-600) 100%);
    color: var(--text-inverse);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    box-shadow: var(--shadow-lg);
}

.banner-text h3 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.banner-text p {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin: 0;
}

.banner-action {
    flex-shrink: 0;
}

.btn-ship {
    background: linear-gradient(135deg, var(--success) 0%, var(--success-600) 100%);
    color: var(--text-inverse);
    border: none;
    border-radius: var(--radius-xl);
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    transition: all var(--transition-base);
    box-shadow: var(--shadow-md);
}

.btn-ship:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 197, 94, 0.3);
    color: var(--text-inverse);
    text-decoration: none;
}

/* === PREMIUM CARDS === */
.card {
    background: var(--surface);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--surface) 100%);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--space-4) var(--space-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.card-title i {
    color: var(--primary);
    font-size: var(--font-size-base);
}

.card-body {
    padding: var(--space-6);
}

/* === PREMIUM DATA TABLES === */
.table-container {
    background: var(--surface);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--space-6);
}

.table-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--dental-blue-light) 100%);
    color: var(--text-inverse);
    padding: var(--space-3) var(--space-4); /* Reduced padding */
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-title {
    font-size: 0.9rem; /* Smaller font */
    font-weight: var(--font-weight-bold);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-1); /* Smaller gap */
}

.table-title i {
    font-size: 0.8rem; /* Smaller icon */
}

.table-actions {
    display: flex;
    gap: var(--space-2);
}

.table-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--text-inverse);
    border-radius: var(--radius-lg);
    padding: 4px 8px; /* Smaller padding */
    font-size: 0.7rem; /* Smaller font */
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-base);
    backdrop-filter: blur(10px);
}

.table-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.75rem; /* Smaller base font */
    background: var(--surface);
}

.data-table thead {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.data-table th {
    background: transparent;
    color: var(--text-secondary);
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 0.05em; /* Reduced letter spacing */
    padding: var(--space-2) var(--space-3); /* Reduced padding */
    text-align: left;
    border-bottom: 2px solid var(--primary);
    font-size: 0.65rem; /* Smaller header font */
    position: relative;
    white-space: nowrap; /* Prevent text wrapping */
}

.data-table th::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary) 0%, var(--info) 100%);
    transition: width var(--transition-base);
}

.data-table th:hover::after {
    width: 100%;
}

.data-table tbody tr {
    transition: all var(--transition-base);
    border-bottom: 1px solid var(--border-secondary);
}

.data-table tbody tr:hover {
    background: linear-gradient(135deg, var(--primary-alpha) 0%, var(--info-alpha) 100%);
    transform: scale(1.01);
    box-shadow: var(--shadow-md);
}

.data-table tbody tr:nth-child(even) {
    background: rgba(0, 0, 0, 0.02);
}

.data-table tbody tr:nth-child(even):hover {
    background: linear-gradient(135deg, var(--primary-alpha) 0%, var(--info-alpha) 100%);
}

.data-table td {
    padding: var(--space-2) var(--space-3); /* Reduced padding */
    color: var(--text-primary);
    vertical-align: middle;
    border-bottom: 1px solid var(--border-secondary);
    position: relative;
    font-size: 0.75rem; /* Consistent smaller font */
}

.data-table td:first-child {
    border-left: 3px solid transparent;
    transition: border-color var(--transition-base);
}

.data-table tbody tr:hover td:first-child {
    border-left-color: var(--primary);
}

/* Premium Table Cell Types */
.table-cell-primary {
    font-weight: var(--font-weight-bold);
    color: var(--primary);
}

.table-cell-number {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-weight: var(--font-weight-semibold);
    text-align: right;
}

.table-cell-date {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.table-cell-priority {
    text-align: center;
}

/* Premium Table Responsive */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: var(--radius-full);
}

.table-responsive::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, var(--primary) 0%, var(--info) 100%);
    border-radius: var(--radius-full);
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, var(--primary-hover) 0%, var(--info-600) 100%);
}

.case-number {
    color: var(--primary);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    font-size: 0.75rem; /* Smaller font */
    white-space: nowrap;
    transition: all var(--transition-base);
}

.case-number:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* === PREMIUM STATUS BADGES === */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 3px; /* Smaller gap */
    padding: 3px 8px; /* Smaller padding */
    border-radius: var(--radius-full);
    font-size: 0.6rem; /* Smaller font */
    font-weight: var(--font-weight-bold);
    text-transform: uppercase;
    letter-spacing: 0.05em; /* Reduced letter spacing */
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    white-space: nowrap; /* Prevent wrapping */
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-slow);
}

.status-badge:hover::before {
    left: 100%;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.status-badge i {
    font-size: 0.55rem; /* Smaller icon */
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.status-pending-acceptance {
    background: linear-gradient(135deg, var(--warning-alpha) 0%, var(--warning-light) 100%);
    color: var(--warning-600);
    border: 1px solid var(--warning-alpha);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.status-pending-acceptance i {
    color: var(--warning-500);
}

.status-in-progress {
    background: linear-gradient(135deg, var(--info-alpha) 0%, var(--info-light) 100%);
    color: var(--info-600);
    border: 1px solid var(--info-alpha);
    box-shadow: 0 4px 12px rgba(6, 182, 212, 0.2);
}

.status-in-progress i {
    color: var(--info-500);
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.status-ready-to-ship {
    background: linear-gradient(135deg, var(--success-alpha) 0%, var(--success-light) 100%);
    color: var(--success-600);
    border: 1px solid var(--success-alpha);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
}

.status-ready-to-ship i {
    color: var(--success-500);
}

.status-shipped {
    background: linear-gradient(135deg, var(--primary-alpha) 0%, var(--primary-light) 100%);
    color: var(--primary-600);
    border: 1px solid var(--primary-alpha);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.status-shipped i {
    color: var(--primary-500);
}

.status-delivered {
    background: linear-gradient(135deg, var(--success-alpha) 0%, var(--success-light) 100%);
    color: var(--success-600);
    border: 1px solid var(--success-alpha);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
}

.status-delivered i {
    color: var(--success-500);
}

.status-on-hold {
    background: linear-gradient(135deg, var(--danger-alpha) 0%, var(--danger-light) 100%);
    color: var(--danger-600);
    border: 1px solid var(--danger-alpha);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.status-on-hold i {
    color: var(--danger-500);
}

/* Premium Priority Indicators */
.priority-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px; /* Smaller size */
    height: 24px;
    border-radius: var(--radius-full);
    font-size: 0.6rem; /* Smaller font */
    font-weight: var(--font-weight-bold);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.priority-high {
    background: linear-gradient(135deg, var(--danger) 0%, var(--danger-600) 100%);
    color: var(--text-inverse);
}

.priority-medium {
    background: linear-gradient(135deg, var(--warning) 0%, var(--warning-600) 100%);
    color: var(--text-inverse);
}

.priority-low {
    background: linear-gradient(135deg, var(--success) 0%, var(--success-600) 100%);
    color: var(--text-inverse);
}

.priority-badge:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

/* === DENTIST INFO === */
.dentist-info {
    display: flex;
    align-items: center;
    gap: var(--space-2); /* Reduced gap */
}

.avatar {
    width: 32px; /* Smaller avatar */
    height: 32px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    font-size: 0.7rem; /* Smaller font */
    color: var(--text-inverse);
    background: linear-gradient(135deg, var(--primary) 0%, var(--purple) 100%);
    box-shadow: var(--shadow-sm);
    flex-shrink: 0; /* Prevent shrinking */
}

.dentist-details {
    display: flex;
    flex-direction: column;
    gap: 1px; /* Minimal gap */
    min-width: 0; /* Allow text truncation */
}

.dentist-name {
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    font-size: 0.75rem; /* Smaller font */
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dentist-clinic {
    font-size: 0.65rem; /* Smaller font */
    color: var(--text-tertiary);
    line-height: 1.1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* === ACTIONS GROUP === */
.actions-group {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.actions-group .btn-action {
    width: 28px; /* Smaller buttons */
    height: 28px;
    border-radius: var(--radius-lg);
    font-size: 0.7rem; /* Smaller font */
    border: 1px solid var(--border-primary);
    background: var(--surface);
    color: var(--text-secondary);
    transition: all var(--transition-base);
}

.actions-group .btn-action:hover {
    background: var(--primary);
    color: var(--text-inverse);
    border-color: var(--primary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* === EMPTY STATES === */
.empty-state {
    text-align: center;
    padding: var(--space-12) var(--space-6);
    color: var(--text-tertiary);
}

.empty-state i {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--space-4);
    opacity: 0.5;
}

.empty-state-text {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    margin: 0;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
    .dashboard-content {
        padding: var(--space-4) var(--space-3);
    }

    .dashboard-header-content {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-4);
    }

    .dashboard-title {
        font-size: var(--font-size-3xl);
    }

    .dashboard-actions {
        flex-direction: column;
        gap: var(--space-3);
    }

    .search-container {
        min-width: auto;
    }

    .metrics-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .ready-to-ship-banner {
        flex-direction: column;
        text-align: center;
        gap: var(--space-4);
    }

    .banner-content {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .metric-card-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }

    .metric-icon {
        width: 56px;
        height: 56px;
    }

    .metric-value {
        font-size: var(--font-size-2xl);
    }

    .table-container {
        margin: var(--space-2);
    }

    .table-header {
        flex-direction: column;
        gap: var(--space-3);
        text-align: center;
    }

    .data-table th,
    .data-table td {
        padding: 4px 6px; /* Even smaller padding for mobile */
        font-size: 0.65rem; /* Smaller font for mobile */
    }

    .dentist-info {
        flex-direction: column;
        text-align: center;
        gap: var(--space-2);
    }

    .avatar {
        width: 24px; /* Even smaller for mobile */
        height: 24px;
        font-size: 0.6rem; /* Smaller font for mobile */
    }

    .status-badge {
        font-size: 0.55rem; /* Even smaller for mobile */
        padding: 2px 6px;
    }

    .priority-badge {
        width: 20px; /* Smaller for mobile */
        height: 20px;
        font-size: 0.55rem;
    }

    .actions-group .btn-action {
        width: 24px; /* Smaller for mobile */
        height: 24px;
        font-size: 0.6rem;
    }
}

/* === PREMIUM TABLE ANIMATIONS === */
@keyframes tableRowSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes tableHeaderGlow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(37, 99, 235, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(37, 99, 235, 0.6);
    }
}

.data-table tbody tr {
    animation: tableRowSlideIn 0.3s ease-out forwards;
}

.data-table tbody tr:nth-child(1) { animation-delay: 0.1s; }
.data-table tbody tr:nth-child(2) { animation-delay: 0.2s; }
.data-table tbody tr:nth-child(3) { animation-delay: 0.3s; }
.data-table tbody tr:nth-child(4) { animation-delay: 0.4s; }
.data-table tbody tr:nth-child(5) { animation-delay: 0.5s; }

.table-header:hover {
    animation: tableHeaderGlow 2s ease-in-out infinite;
}

/* === LOADING STATES === */
.table-loading {
    position: relative;
    overflow: hidden;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: tableShimmer 1.5s infinite;
}

@keyframes tableShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* === PREMIUM HOVER EFFECTS === */
.data-table tbody tr:hover .avatar {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.data-table tbody tr:hover .status-badge {
    transform: scale(1.05);
}

.data-table tbody tr:hover .priority-badge {
    transform: scale(1.2) rotate(5deg);
}

.table-btn:active {
    transform: scale(0.95);
}

/* === PREMIUM SCROLLBAR === */
.table-responsive::-webkit-scrollbar-corner {
    background: var(--bg-secondary);
}
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <div class="dashboard-content">
        <!-- Premium Dashboard Header -->
        <header class="dashboard-header">
            <div class="dashboard-header-content">
                <div class="dashboard-title-section">
                    <h1 class="dashboard-title">Dental Lab Dashboard</h1>
                    <p class="dashboard-subtitle">
                        <i class="bi bi-activity"></i>
                        Real-time insights and case management
                    </p>
                </div>
                <div class="dashboard-actions">
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <i class="bi bi-search"></i>
                            <input type="text" class="search-input" placeholder="Search cases, patients, dentists..." id="globalSearch">
                        </div>
                    </div>
                    <div class="search-container">
                        <div class="search-input-wrapper">
                            <i class="bi bi-calendar3"></i>
                            <select class="search-input" id="dateRangeFilter" aria-label="Select Date Range">
                                <option value="7" {% if selected_range == '7' %}selected{% endif %}>Last 7 Days</option>
                                <option value="30" {% if selected_range == '30' %}selected{% endif %}>Last 30 Days</option>
                                <option value="180" {% if selected_range == '180' %}selected{% endif %}>Last 6 Months</option>
                                <option value="365" {% if selected_range == '365' %}selected{% endif %}>Last 12 Months</option>
                            </select>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="btn-action" title="Refresh Data" id="refreshBtn" aria-label="Refresh Dashboard Data">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        <button class="btn-action btn-primary" title="Dark Mode Toggle" id="darkModeToggle" aria-label="Toggle Dark Mode">
                            <i class="bi bi-moon-stars"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

    {% if error %}
    <div class="alert alert-danger" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i> {{ error }} Please check system logs or contact support.
    </div>
    {% else %}

    <!-- Metrics Grid -->
    <div class="metrics-grid">
        <div class="metric-card accent-primary">
            <div class="metric-card-content">
                <div class="metric-icon icon-primary"><i class="bi bi-folder2-open"></i></div>
                <div class="metric-content">
                    <span class="metric-label">Total Cases</span>
                    <div class="metric-value">{{ total_cases_count|intcomma }}</div>
                    <div class="metric-comparison"><i class="bi bi-info-circle"></i> All-time record</div>
                </div>
            </div>
        </div>

        <div class="metric-card accent-success">
            <div class="metric-card-content">
                <div class="metric-icon icon-success"><i class="bi bi-calendar-plus"></i></div>
                <div class="metric-content">
                    <span class="metric-label">Cases Today</span>
                    <div class="metric-value">{{ cases_today_count|intcomma }}</div>
                    <div class="metric-comparison">New cases received</div>
                </div>
            </div>
        </div>

        <div class="metric-card accent-info">
            <div class="metric-card-content">
                <div class="metric-icon icon-info"><i class="bi bi-calendar-week"></i></div>
                <div class="metric-content">
                    <span class="metric-label">This Week</span>
                    <div class="metric-value">{{ cases_this_week_count|intcomma }}</div>
                    <div class="metric-comparison">
                        {% if week_difference is not None and week_difference != 0 %}
                            {% if week_difference > 0 %}
                                <span class="trend-up"><i class="bi bi-arrow-up-short"></i> {{ week_difference|abs|intcomma }} more</span>
                            {% elif week_difference < 0 %}
                                <span class="trend-down"><i class="bi bi-arrow-down-short"></i> {{ week_difference|abs|intcomma }} less</span>
                            {% endif %}
                            than last week
                        {% elif week_difference == 0 %}
                            <span class="trend-neutral"><i class="bi bi-dash"></i> Same as last week</span>
                        {% else %}
                            <span class="trend-neutral"><i class="bi bi-info-circle"></i> No comparison data</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="metric-card accent-warning">
            <div class="metric-card-content">
                <div class="metric-icon icon-warning"><i class="bi bi-calendar-month"></i></div>
                <div class="metric-content">
                    <span class="metric-label">This Month</span>
                    <div class="metric-value">{{ cases_this_month_count|intcomma }}</div>
                    <div class="metric-comparison">
                        {% if month_difference is not None and month_difference != 0 %}
                            {% if month_difference > 0 %}
                                <span class="trend-up"><i class="bi bi-arrow-up-short"></i> {{ month_difference|abs|intcomma }} more</span>
                            {% elif month_difference < 0 %}
                                <span class="trend-down"><i class="bi bi-arrow-down-short"></i> {{ month_difference|abs|intcomma }} less</span>
                            {% endif %}
                            than last month
                        {% elif month_difference == 0 %}
                            <span class="trend-neutral"><i class="bi bi-dash"></i> Same as last month</span>
                        {% else %}
                            <span class="trend-neutral"><i class="bi bi-info-circle"></i> No comparison data</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="metric-card accent-danger">
            <div class="metric-card-content">
                <div class="metric-icon icon-danger"><i class="bi bi-exclamation-octagon"></i></div>
                <div class="metric-content">
                    <span class="metric-label">Overdue Cases</span>
                    <div class="metric-value">{{ overdue_cases_count|intcomma }}</div>
                    <div class="metric-comparison">Past their deadline</div>
                </div>
            </div>
        </div>

        <div class="metric-card accent-purple">
            <div class="metric-card-content">
                <div class="metric-icon icon-purple"><i class="bi bi-box-seam"></i></div>
                <div class="metric-content">
                    <span class="metric-label">Ready to Ship</span>
                    <div class="metric-value">{{ ready_to_ship_count|intcomma }}</div>
                    <div class="metric-comparison">Awaiting shipment</div>
                </div>
            </div>
        </div>

        <div class="metric-card accent-success">
            <div class="metric-card-content">
                <div class="metric-icon icon-success"><i class="bi bi-stopwatch"></i></div>
                <div class="metric-content">
                    <span class="metric-label">Avg. Completion</span>
                    <div class="metric-value">{{ avg_completion_days|floatformat:1 }}<span class="unit">days</span></div>
                    <div class="metric-comparison">Based on selected range</div>
                </div>
            </div>
        </div>

        <div class="metric-card accent-primary">
            <div class="metric-card-content">
                <div class="metric-icon icon-primary"><i class="bi bi-check2-circle"></i></div>
                <div class="metric-content">
                    <span class="metric-label">On-Time Rate</span>
                    <div class="metric-value">{{ on_time_completion_rate|floatformat:1 }}<span class="unit">%</span></div>
                    <div class="metric-comparison">Based on selected range</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications Section -->
    {% if notifications %}
    <div class="notifications-container mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title"><i class="bi bi-bell"></i>Notifications</h5>
                <button class="btn-action" title="Mark All as Read" aria-label="Mark All Notifications as Read">
                    <i class="bi bi-check-all"></i>
                </button>
            </div>
            <div class="card-body p-0">
                <ul class="notification-list">
                    {% for notification in notifications %}
                    <li class="notification-item {% if notification.is_important %}notification-important{% elif notification.is_urgent %}notification-urgent{% endif %}">
                        <div class="notification-icon">
                            <i class="{{ notification.icon_class|default:'bi bi-info-circle' }}"></i>
                        </div>
                        <div class="notification-content">
                            <div class="notification-title">{{ notification.title }}</div>
                            <div class="notification-text">{{ notification.text }}</div>
                            <div class="notification-time">{{ notification.timestamp|timesince }} ago</div>
                        </div>
                        {% if notification.action_url %}
                        <div class="notification-actions">
                            <a href="{{ notification.action_url }}" class="btn {{ notification.action_class|default:'btn-primary' }} btn-sm">{{ notification.action_text|default:'View' }}</a>
                        </div>
                        {% endif %}
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Ready to Ship Banner -->
    {% if ready_to_ship_count > 0 %}
    <div class="ready-to-ship-banner">
        <div class="banner-content">
            <div class="banner-icon"><i class="bi bi-box-seam"></i></div>
            <div class="banner-text">
                <h3>Ready to Ship Cases</h3>
                <p>You have <strong>{{ ready_to_ship_count }} {{ ready_to_ship_count|pluralize:"case,cases" }}</strong> ready for shipment. Process them for timely delivery.</p>
            </div>
        </div>
        <div class="banner-action">
            <a href="{% url 'case:case_list' %}?status=ready_to_ship" class="btn-ship">Process Shipments <i class="bi bi-arrow-right"></i></a>
        </div>
    </div>
    {% endif %}

    <!-- Charts Row 1: Trend -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card" style="border-radius: var(--border-radius-lg); overflow: hidden; box-shadow: var(--shadow-lg);">
                <div class="card-header" style="background: linear-gradient(135deg, var(--primary-alpha) 0%, transparent 100%); border-bottom: 2px solid var(--primary-alpha);">
                    <h5 class="card-title" style="font-size: 1.25rem; font-weight: 700;"><i class="bi bi-graph-up" style="color: var(--primary); margin-right: 10px;"></i>Case Volume Trends</h5>
                    <div class="tab-controls" style="background: var(--card-bg); border: 1px solid var(--primary-alpha); box-shadow: var(--shadow-sm);">
                        <button class="tab-control active" id="trend30DaysBtn" aria-pressed="true" style="font-weight: 600;">Last 30 Days</button>
                        <button class="tab-control" id="trend12MonthsBtn" aria-pressed="false" style="font-weight: 600;">Last 12 Months</button>
                    </div>
                </div>
                <div class="card-body" style="padding: var(--space-xl); background: linear-gradient(180deg, var(--card-bg) 0%, var(--bg-main) 100%);">
                    <div class="chart-container" style="height: 380px; margin-top: 10px;">
                        <canvas id="trendChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2: Breakdowns -->
    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title"><i class="bi bi-pie-chart"></i>Cases by Status</h5>
                </div>
                <div class="card-body d-flex align-items-center justify-content-center">
                    <div class="chart-container chart-container-doughnut">
                         {% if status_chart_data %}
                            <canvas id="statusChart"></canvas>
                         {% else %}
                            <div class="empty-state text-center">
                                <i class="bi bi-bar-chart-line"></i>
                                <p class="empty-state-text">No status data available</p>
                            </div>
                         {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title"><i class="bi bi-diagram-3"></i>Cases by Priority</h5>
                </div>
                <div class="card-body d-flex align-items-center justify-content-center">
                    <div class="chart-container chart-container-doughnut">
                         {% if priority_chart_data %}
                            <canvas id="priorityChart"></canvas>
                         {% else %}
                            <div class="empty-state text-center">
                                <i class="bi bi-bar-chart-line"></i>
                                <p class="empty-state-text">No priority data available</p>
                            </div>
                         {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="card-title"><i class="bi bi-building"></i>Cases by Department</h5>
                </div>
                <div class="card-body">
                     {% if department_chart_data %}
                        <div class="chart-container" style="height: 280px;"> <!-- Adjust height for bar chart -->
                            <canvas id="departmentChart"></canvas>
                        </div>
                     {% else %}
                        <div class="empty-state text-center" style="padding-top: 2rem; padding-bottom: 2rem;">
                            <i class="bi bi-bar-chart-line"></i>
                            <p class="empty-state-text">No department data available</p>
                        </div>
                     {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Tables Row -->
    <div class="row">
        <div class="col-lg-8 mb-4">
            <div class="table-container">
                <div class="table-header">
                    <h5 class="table-title"><i class="bi bi-journal-text"></i>Latest Cases</h5>
                    <div class="table-actions">
                        <button class="table-btn" title="Export Data">
                            <i class="bi bi-download"></i>
                        </button>
                        <a href="{% url 'case:case_list' %}" class="table-btn" title="View All Cases">
                            View All
                        </a>
                    </div>
                </div>
                {% if latest_cases %}
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Case #</th>
                                <th>Patient</th>
                                <th>Dentist</th>
                                <th>Status</th>
                                <th>Received</th>
                                <th>Priority</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for case in latest_cases %}
                            <tr>
                                <td class="table-cell-primary">
                                    <a href="{% url 'case:case_detail' case.pk %}" class="case-number">{{ case.case_number }}</a>
                                </td>
                                <td>
                                    <div class="dentist-info">
                                        <div class="avatar">{{ case.patient.get_full_name|first|upper|default:"P" }}</div>
                                        <div class="dentist-details">
                                            <span class="dentist-name">{{ case.patient.get_full_name|default:"N/A" }}</span>
                                            <span class="dentist-clinic">Patient ID: {{ case.patient.id|default:"--" }}</span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="dentist-info">
                                        <div class="avatar">{{ case.dentist.get_full_name|first|upper|default:"D" }}</div>
                                        <div class="dentist-details">
                                            <span class="dentist-name">{{ case.dentist.get_full_name|default:"N/A" }}</span>
                                            <span class="dentist-clinic">{{ case.dentist.clinic_name|default:"--" }}</span>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="status-badge status-{{ case.status|lower|slugify|default:'default' }}">
                                        <i class="bi
                                            {% if case.status == 'pending_acceptance' %}bi-hourglass-split
                                            {% elif case.status == 'in_progress' %}bi-gear
                                            {% elif case.status == 'on_hold' %}bi-pause-fill
                                            {% elif case.status == 'ready_to_ship' %}bi-box
                                            {% elif case.status == 'shipped' %}bi-truck
                                            {% elif case.status == 'delivered' %}bi-check2-circle
                                            {% elif case.status == 'closed' %}bi-archive
                                            {% else %}bi-question-circle{% endif %}"></i>
                                        {{ case.get_status_display|default:case.status }}
                                    </span>
                                </td>
                                <td class="table-cell-date">{{ case.received_date_time|date:"d M, Y"|default:"N/A" }}</td>
                                <td class="table-cell-priority">
                                    <span class="priority-badge priority-{{ case.priority|default:'medium' }}">
                                        {% if case.priority == 'high' %}H
                                        {% elif case.priority == 'low' %}L
                                        {% else %}M{% endif %}
                                    </span>
                                </td>
                                <td>
                                    <div class="actions-group">
                                        <a href="{% url 'case:case_detail' case.pk %}" class="btn-action" title="View Details" aria-label="View Case {{ case.case_number }}">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <a href="{% url 'case:case_update' case.pk %}" class="btn-action" title="Edit Case" aria-label="Edit Case {{ case.case_number }}">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <button class="btn-action" title="Quick Actions" aria-label="Quick Actions for {{ case.case_number }}">
                                            <i class="bi bi-three-dots"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="empty-state">
                    <i class="bi bi-folder-x"></i>
                    <p class="empty-state-text">No recent cases found in the selected range</p>
                </div>
                {% endif %}
            </div>
        </div>

        <div class="col-lg-4 mb-4">
            <div class="table-container">
                <div class="table-header">
                    <h5 class="table-title"><i class="bi bi-person-badge"></i>Top Dentists</h5>
                    <div class="table-actions">
                        <button class="table-btn" title="Export Data">
                            <i class="bi bi-download"></i>
                        </button>
                        <a href="{% url 'Dentists:dentist_list' %}" class="table-btn" title="View All Dentists">
                            View All
                        </a>
                    </div>
                </div>
                {% if top_dentists %}
                <div class="table-responsive">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Dentist</th>
                                <th class="table-cell-number">Cases</th>
                                <th>Rating</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for dentist in top_dentists %}
                            {% with initial=dentist.get_full_name|first|upper %}
                            <tr>
                                <td>
                                    <div class="dentist-info">
                                        <div class="avatar avatar-{{ initial }}">{{ initial }}</div>
                                        <div class="dentist-details">
                                            <span class="dentist-name">{{ dentist.get_full_name }}</span>
                                            <span class="dentist-clinic">{{ dentist.clinic_name|default:"Independent Practice" }}</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="table-cell-number">
                                    <span class="metric-value" style="font-size: var(--font-size-lg); margin-bottom: 0;">{{ dentist.total_cases_last_year|intcomma }}</span>
                                    <div style="font-size: var(--font-size-xs); color: var(--text-tertiary);">this year</div>
                                </td>
                                <td class="table-cell-priority">
                                    <div style="display: flex; align-items: center; gap: var(--space-1);">
                                        {% for i in "12345" %}
                                            <i class="bi bi-star{% if forloop.counter <= 4 %}-fill{% endif %}" style="color: var(--warning); font-size: var(--font-size-xs);"></i>
                                        {% endfor %}
                                        <span style="font-size: var(--font-size-xs); color: var(--text-secondary); margin-left: var(--space-1);">4.8</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="actions-group">
                                        <a href="{% url 'Dentists:dentist_detail' dentist.pk %}" class="btn-action" title="View Profile" aria-label="View Profile for {{ dentist.get_full_name }}">
                                            <i class="bi bi-person"></i>
                                        </a>
                                        <button class="btn-action" title="Send Message" aria-label="Send Message to {{ dentist.get_full_name }}">
                                            <i class="bi bi-chat-dots"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endwith %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="empty-state">
                    <i class="bi bi-people"></i>
                    <p class="empty-state-text">No active dentists found</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

        {% endif %} {# End of {% if not error %} #}

        <!-- Store the trend data in JSON format (outside error check) -->
        {% if not error %}
            {{ trend_30_days_json|json_script:"trend-30-data" }}
            {{ trend_12_months_json|json_script:"trend-12-data" }}
        {% else %}
            <script id="trend-30-data" type="application/json">[]</script>
            <script id="trend-12-data" type="application/json">[]</script>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@2.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/esm/index.js" type="module"></script>

<script type="module">
    // import { format, parseISO } from 'https://cdn.jsdelivr.net/npm/date-fns@2.29.3/esm/index.js'; // Not strictly needed if adapter works

    document.addEventListener('DOMContentLoaded', function() {
        console.log("DOM Loaded. Initializing dashboard JS...");

        // --- Premium Theme System ---
        const darkModeToggle = document.getElementById('darkModeToggle');
        const htmlElement = document.documentElement;
        const prefersDarkScheme = window.matchMedia("(prefers-color-scheme: dark)");
        const currentTheme = localStorage.getItem("theme");
        const initialTheme = currentTheme ? currentTheme : (prefersDarkScheme.matches ? "dark" : "light");
        let currentChartColors;
        const charts = {};

        // Premium theme transition
        function setTheme(theme) {
            htmlElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);

            // Update toggle icon with animation
            const icon = darkModeToggle.querySelector('i');
            icon.style.transform = 'scale(0)';

            setTimeout(() => {
                icon.className = theme === 'dark' ? 'bi bi-sun' : 'bi bi-moon-stars';
                icon.style.transform = 'scale(1)';
            }, 150);

            // Update chart colors
            currentChartColors = getChartColors(theme);
            updateAllCharts();
        }

        // Initialize theme
        setTheme(initialTheme);

        // Theme toggle with premium animation
        darkModeToggle.addEventListener('click', () => {
            const currentTheme = htmlElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            // Add ripple effect
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.3);
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
                left: 50%;
                top: 50%;
                width: 100px;
                height: 100px;
                margin-left: -50px;
                margin-top: -50px;
            `;

            darkModeToggle.style.position = 'relative';
            darkModeToggle.appendChild(ripple);

            setTimeout(() => ripple.remove(), 600);
            setTheme(newTheme);
        });

        // Add ripple animation CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);

        // --- Premium Search Functionality ---
        const searchInput = document.getElementById('globalSearch');
        const refreshBtn = document.getElementById('refreshBtn');

        // Enhanced search with debouncing
        let searchTimeout;
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();

            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    performSearch(query);
                }, 300);
            }
        });

        // Premium search function
        function performSearch(query = null) {
            const searchQuery = query || searchInput.value.trim();
            if (searchQuery.length >= 2) {
                // Add loading state
                searchInput.parentElement.classList.add('loading');

                // Simulate search (replace with actual search logic)
                setTimeout(() => {
                    window.location.href = `/cases/?search=${encodeURIComponent(searchQuery)}`;
                }, 500);
            }
        }

        // Enhanced refresh with animation
        refreshBtn.addEventListener('click', () => {
            refreshBtn.style.transform = 'rotate(360deg)';
            refreshBtn.style.transition = 'transform 0.6s ease-in-out';

            setTimeout(() => {
                window.location.reload();
            }, 300);
        });

        // Add premium loading states
        document.querySelectorAll('.metric-card').forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.style.animation = 'slideInUp 0.6s ease-out forwards';
        });

        // Add slide-in animation CSS
        const animationStyle = document.createElement('style');
        animationStyle.textContent = `
            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .metric-card {
                opacity: 0;
            }
        `;
        document.head.appendChild(animationStyle);

        function getChartColors(theme) {
            const isDark = theme === 'dark';
            // Temporarily set attribute to get correct computed styles IF not already set
            const currentHtmlTheme = htmlElement.getAttribute('data-theme');
            if (!currentHtmlTheme) {
                 htmlElement.setAttribute('data-theme', theme);
            }

            const style = getComputedStyle(document.documentElement);
            const colors = {
                primary: style.getPropertyValue('--primary').trim(),
                success: style.getPropertyValue('--success').trim(),
                danger: style.getPropertyValue('--danger').trim(),
                warning: style.getPropertyValue('--warning').trim(),
                info: style.getPropertyValue('--info').trim(),
                purple: style.getPropertyValue('--purple').trim(),
                gridColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.07)',
                ticksColor: isDark ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                tooltipBg: isDark ? 'rgba(40, 42, 45, 0.9)' : 'rgba(30, 41, 59, 0.9)',
                tooltipColor: isDark ? '#e8eaed' : '#ffffff',
                legendColor: isDark ? 'rgba(255, 255, 255, 0.8)' : 'rgba(0, 0, 0, 0.7)',
                cardBg: style.getPropertyValue('--card-bg').trim(), // Get card background for borders
            };

            // Restore original theme if we temporarily set it
            if (!currentHtmlTheme) {
                htmlElement.removeAttribute('data-theme');
            } else {
                htmlElement.setAttribute('data-theme', currentHtmlTheme); // Ensure it's set back if it existed
            }

            return colors;
        }

        // Initialize currentChartColors BEFORE the first call to setTheme
        currentChartColors = getChartColors(initialTheme);

        function updateChartDefaults() {
            Chart.defaults.font.family = "'Inter', -apple-system, BlinkMacSystemFont, sans-serif";
            Chart.defaults.font.size = 13;
            Chart.defaults.plugins.legend.labels.usePointStyle = true;
            Chart.defaults.plugins.legend.position = 'bottom';
            Chart.defaults.plugins.tooltip.backgroundColor = currentChartColors.tooltipBg;
            Chart.defaults.plugins.tooltip.titleColor = currentChartColors.tooltipColor;
            Chart.defaults.plugins.tooltip.bodyColor = currentChartColors.tooltipColor;
            Chart.defaults.plugins.tooltip.titleFont = { weight: '600', size: 14 };
            Chart.defaults.plugins.tooltip.bodyFont = { size: 13 };
            Chart.defaults.plugins.tooltip.padding = 12;
            Chart.defaults.plugins.tooltip.cornerRadius = 8;
            Chart.defaults.plugins.tooltip.displayColors = false; // Cleaner look
            Chart.defaults.plugins.tooltip.boxPadding = 6;
        }

        function updateExistingChartOptions() {
             // Update existing charts options that depend on theme colors
            Object.values(charts).forEach(chart => {
                if (chart?.options) { // Check if chart and options exist
                    if(chart.options.plugins?.legend?.labels) {
                        chart.options.plugins.legend.labels.color = currentChartColors.legendColor;
                    }
                    if (chart.options.scales?.x) {
                        if(chart.options.scales.x.grid) chart.options.scales.x.grid.color = currentChartColors.gridColor;
                        if(chart.options.scales.x.ticks) chart.options.scales.x.ticks.color = currentChartColors.ticksColor;
                    }
                    if (chart.options.scales?.y) {
                       if(chart.options.scales.y.grid) chart.options.scales.y.grid.color = currentChartColors.gridColor;
                       if(chart.options.scales.y.ticks) chart.options.scales.y.ticks.color = currentChartColors.ticksColor;
                    }

                     // Update doughnut/pie chart border colors to match new card background
                     if (chart.config.type === 'doughnut' || chart.config.type === 'pie') {
                         chart.config.data.datasets.forEach(dataset => {
                             dataset.borderColor = currentChartColors.cardBg;
                             dataset.hoverBorderColor = currentChartColors.cardBg;
                         });
                     }

                    chart.update(); // Redraw the chart with updated options
                }
            });
        }

        function setTheme(theme) {
            htmlElement.setAttribute('data-theme', theme); // Set the data-theme attribute first
            currentChartColors = getChartColors(theme); // THEN get the new colors
            updateChartDefaults(); // Update Chart.js defaults
            updateExistingChartOptions(); // Update options of existing charts

            if (darkModeToggle) {
                 if (theme === "dark") {
                    darkModeToggle.querySelector('i').classList.remove('bi-moon-stars');
                    darkModeToggle.querySelector('i').classList.add('bi-brightness-high'); // Sun icon
                    darkModeToggle.setAttribute('aria-pressed', 'true');
                } else {
                    darkModeToggle.querySelector('i').classList.remove('bi-brightness-high');
                    darkModeToggle.querySelector('i').classList.add('bi-moon-stars');
                    darkModeToggle.setAttribute('aria-pressed', 'false');
                }
            }
            localStorage.setItem('theme', theme);
        }

        // Set initial theme
        setTheme(initialTheme); // This now initializes defaults and options correctly

        // Listener for toggle button
        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', () => {
                const newTheme = htmlElement.getAttribute('data-theme') === 'dark' ? 'light' : 'dark';
                setTheme(newTheme);
            });
        }

        // Listener for OS theme changes
        prefersDarkScheme.addEventListener('change', (e) => {
            // Only change if no theme is manually set in localStorage
            if (!localStorage.getItem("theme")) {
                setTheme(e.matches ? "dark" : "light");
            }
        });

        // --- Helper Function for Percentage Tooltip ---
        const getOrCreateTooltip = (chart) => {
            let tooltipEl = chart.canvas.parentNode.querySelector('div.chartjs-tooltip');

            if (!tooltipEl) {
                tooltipEl = document.createElement('div');
                tooltipEl.classList.add('chartjs-tooltip');
                tooltipEl.style.opacity = 1; // Start visible
                tooltipEl.style.pointerEvents = 'none';
                tooltipEl.style.position = 'absolute';
                tooltipEl.style.transform = 'translate(-50%, 0)';
                tooltipEl.style.transition = 'all .1s ease';
                tooltipEl.style.zIndex = '10';
                // Apply styles based on currentChartColors
                tooltipEl.style.background = currentChartColors.tooltipBg;
                tooltipEl.style.borderRadius = '8px';
                tooltipEl.style.color = currentChartColors.tooltipColor;
                tooltipEl.style.padding = '10px';
                tooltipEl.style.fontSize = '13px';
                tooltipEl.style.fontFamily = Chart.defaults.font.family;
                tooltipEl.style.boxShadow = '0 3px 8px rgba(0,0,0,0.15)';


                const table = document.createElement('table');
                table.style.margin = '0px';

                tooltipEl.appendChild(table);
                // Append to parentNode to ensure it's within relative positioning context
                chart.canvas.parentNode.appendChild(tooltipEl);
            } else {
                 // Update styles if theme changed
                 tooltipEl.style.background = currentChartColors.tooltipBg;
                 tooltipEl.style.color = currentChartColors.tooltipColor;
            }

            return tooltipEl;
        };

        const externalTooltipHandler = (context) => {
            const {chart, tooltip} = context;
            const tooltipEl = getOrCreateTooltip(chart);

            // Hide if no tooltip
            if (tooltip.opacity === 0) {
                tooltipEl.style.opacity = 0;
                return;
            }

            // Set Text
            if (tooltip.body) {
                const titleLines = tooltip.title || [];
                const bodyLines = tooltip.body.map(b => b.lines);

                // Get data points info
                const dataIndex = tooltip.dataPoints[0]?.dataIndex;
                const datasetIndex = tooltip.dataPoints[0]?.datasetIndex;

                // Check if indices are valid
                if (dataIndex === undefined || datasetIndex === undefined) {
                    tooltipEl.style.opacity = 0; // Hide tooltip if indices are invalid
                    return;
                }

                const tableHead = document.createElement('thead');
                tableHead.style.fontWeight = '600';

                titleLines.forEach(title => {
                    const tr = document.createElement('tr');
                    tr.style.borderWidth = 0;

                    const th = document.createElement('th');
                    th.style.borderWidth = 0;
                    th.style.textAlign = 'left';
                    th.style.paddingBottom = '5px';

                    const text = document.createTextNode(title);

                    th.appendChild(text);
                    tr.appendChild(th);
                    tableHead.appendChild(tr);
                });

                const tableBody = document.createElement('tbody');
                bodyLines.forEach((body, i) => {
                    // Ensure labelColors exists and has the current index
                    if (!tooltip.labelColors || !tooltip.labelColors[i]) return;

                    const colors = tooltip.labelColors[i];
                    const dataset = chart.config.data.datasets[datasetIndex];

                     // Ensure dataset and data exist and index is valid
                    if (!dataset || !dataset.data || dataset.data.length <= dataIndex) return;

                    const dataPoint = dataset.data[dataIndex]; // Could be {x,y} or just y
                    const value = typeof dataPoint === 'object' ? dataPoint.y : dataPoint; // Get the y value

                     // Ensure value is valid
                     if (value === undefined || value === null) return;

                    const total = chart.config.data.datasets[datasetIndex].data.reduce((acc, curr) => {
                        // Handle both {x,y} and simple y values in total calculation
                        const yVal = typeof curr === 'object' ? curr.y : curr;
                        return acc + (Number(yVal) || 0);
                     }, 0); // Sum numbers

                    const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;

                    const span = document.createElement('span');
                    span.style.background = colors.backgroundColor;
                    span.style.borderColor = colors.borderColor;
                    span.style.borderWidth = '2px';
                    span.style.marginRight = '8px';
                    span.style.height = '10px';
                    span.style.width = '10px';
                    span.style.borderRadius = '50%';
                    span.style.display = 'inline-block';

                    const tr = document.createElement('tr');
                    tr.style.backgroundColor = 'inherit';
                    tr.style.borderWidth = 0;

                    const td = document.createElement('td');
                    td.style.borderWidth = 0;
                    td.style.padding = '3px 0';

                    // Use label from the chart data configuration
                    const label = chart.config.data.labels[dataIndex] || '';
                    const formattedValue = typeof value === 'number' ? value.toLocaleString() : value;
                    const text = document.createTextNode(`${label}: ${formattedValue} (${percentage}%)`);

                    td.appendChild(span);
                    td.appendChild(text);
                    tr.appendChild(td);
                    tableBody.appendChild(tr);
                });

                const tableRoot = tooltipEl.querySelector('table');

                // Remove old children
                while (tableRoot.firstChild) {
                    tableRoot.firstChild.remove();
                }

                // Add new children
                tableRoot.appendChild(tableHead);
                tableRoot.appendChild(tableBody);
            }

            const {offsetLeft: positionX, offsetTop: positionY} = chart.canvas;

            // Display, position, and set styles for font
            tooltipEl.style.opacity = 1;
            tooltipEl.style.left = positionX + tooltip.caretX + 'px';
            tooltipEl.style.top = positionY + tooltip.caretY + 'px';
            tooltipEl.style.font = `${Chart.defaults.font.style || 'normal'} ${Chart.defaults.font.weight || 'normal'} ${Chart.defaults.font.size || 13}px ${Chart.defaults.font.family || 'sans-serif'}`;
            tooltipEl.style.padding = Chart.defaults.plugins.tooltip.padding + 'px';
        };


        // --- Status Chart (Doughnut) ---
        const statusCtx = document.getElementById('statusChart')?.getContext('2d');
        if (statusCtx) {
            const statusDataRaw = [ {% for item in status_chart_data %}{ label: "{{ item.status|default:'Unknown'|title|escapejs }}", value: {{ item.total|default:0 }} }, {% endfor %} ];
            if (statusDataRaw.length > 0 && statusDataRaw.some(item => item.value > 0)) {
                charts.status = new Chart(statusCtx, {
                    type: 'doughnut',
                    data: {
                        labels: statusDataRaw.map(item => item.label),
                        datasets: [{
                            data: statusDataRaw.map(item => item.value),
                            backgroundColor: [ currentChartColors.primary, currentChartColors.success, currentChartColors.warning, currentChartColors.danger, currentChartColors.purple, currentChartColors.info, '#6c757d', '#adb5bd' ].slice(0, statusDataRaw.length),
                            borderWidth: 2, borderColor: currentChartColors.cardBg, hoverOffset: 8, hoverBorderColor: currentChartColors.cardBg
                        }]
                    },
                    options: {
                        responsive: true, maintainAspectRatio: false, cutout: '70%',
                        plugins: {
                            legend: { labels: { padding: 15, color: currentChartColors.legendColor, boxWidth: 12, font: { size: 12 } } },
                            tooltip: { enabled: false, external: externalTooltipHandler }
                        }
                    }
                });
            } else { statusCtx.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-pie-chart"></i><p class="empty-state-text">No status data</p></div>'; }
        }

        // --- Priority Chart (Doughnut) ---
        const priorityCtx = document.getElementById('priorityChart')?.getContext('2d');
        if (priorityCtx) {
            const priorityDataRaw = [ {% for item in priority_chart_data %}{ label: "{{ item.priority|default:'Unknown'|escapejs }}", value: {{ item.total|default:0 }} }, {% endfor %} ];
            const priorityOrder = {'Urgent': 1, 'High': 2, 'Medium': 3, 'Low': 4};
            priorityDataRaw.sort((a, b) => (priorityOrder[a.label] || 99) - (priorityOrder[b.label] || 99));
            if (priorityDataRaw.length > 0 && priorityDataRaw.some(item => item.value > 0)) {
                const priorityColorsMap = { 'Urgent': currentChartColors.danger, 'High': currentChartColors.warning, 'Medium': currentChartColors.info, 'Low': currentChartColors.success, 'Default': currentChartColors.purple };
                charts.priority = new Chart(priorityCtx, {
                    type: 'doughnut',
                    data: {
                        labels: priorityDataRaw.map(item => item.label),
                        datasets: [{
                            data: priorityDataRaw.map(item => item.value),
                            backgroundColor: priorityDataRaw.map(item => priorityColorsMap[item.label] || priorityColorsMap['Default']),
                            borderWidth: 2, borderColor: currentChartColors.cardBg, hoverOffset: 8, hoverBorderColor: currentChartColors.cardBg
                        }]
                    },
                    options: {
                        responsive: true, maintainAspectRatio: false, cutout: '70%',
                        plugins: {
                            legend: { labels: { padding: 15, color: currentChartColors.legendColor, boxWidth: 12, font: { size: 12 } } },
                            tooltip: { enabled: false, external: externalTooltipHandler }
                        }
                    }
                });
            } else { priorityCtx.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-diagram-3"></i><p class="empty-state-text">No priority data</p></div>'; }
        }

        // --- Department Chart (Bar) ---
        const departmentCtx = document.getElementById('departmentChart')?.getContext('2d');
        if (departmentCtx) {
            const departmentDataRaw = [ {% for item in department_chart_data %}{ label: "{{ item.responsible_department__name|default:'Unassigned'|escapejs }}", value: {{ item.total_cases|default:0 }} }, {% endfor %} ];
            if (departmentDataRaw.length > 0 && departmentDataRaw.some(item => item.value > 0)) {
                departmentDataRaw.sort((a, b) => b.value - a.value);
                const backgroundColors = [ currentChartColors.primary, currentChartColors.info, currentChartColors.purple, currentChartColors.success, currentChartColors.warning, currentChartColors.danger, '#6c757d', '#adb5bd' ];
                charts.department = new Chart(departmentCtx, {
                    type: 'bar',
                    data: {
                        labels: departmentDataRaw.map(item => item.label),
                        datasets: [{
                            label: 'Cases', data: departmentDataRaw.map(item => item.value),
                            backgroundColor: backgroundColors.slice(0, departmentDataRaw.length).map(color => `${color}E6`),
                            borderColor: backgroundColors.slice(0, departmentDataRaw.length),
                            borderWidth: 1, borderRadius: 6, maxBarThickness: 30
                        }]
                    },
                    options: {
                        indexAxis: 'y', responsive: true, maintainAspectRatio: false,
                        scales: {
                            x: { beginAtZero: true, grid: { display: true, drawBorder: false, color: currentChartColors.gridColor, drawOnChartArea: true, }, ticks: { precision: 0, color: currentChartColors.ticksColor, font: { size: 11 } } },
                            y: { grid: { display: false }, ticks: { color: currentChartColors.ticksColor, font: { size: 12 } } }
                        },
                        plugins: {
                            legend: { display: false },
                            tooltip: { enabled: true, callbacks: { label: (ctx) => ` Cases: ${ctx.parsed.x !== undefined ? ctx.parsed.x.toLocaleString() : 'N/A'}` }}
                        }
                    }
                });
            } else { departmentCtx.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-building"></i><p class="empty-state-text">No department data</p></div>'; }
        }


        // --- Trend Chart (Line) ---
        console.log("Setting up Trend Chart...");
        console.log("Chart.js Date Adapter:", Chart._adapters._date ? 'Registered' : 'NOT Registered!');

        const trendCanvasElement = document.getElementById('trendChart');
        const trendCtx = trendCanvasElement?.getContext('2d');
        const trend30DataElement = document.getElementById('trend-30-data');
        const trend12DataElement = document.getElementById('trend-12-data');

        let trend30DataRaw = null;
        let trend12DataRaw = null;
        let formatted30DayData = [];
        let formatted12MonthData = [];

        // Enhanced Data Retrieval and Parsing with Better Error Handling
        if (trend30DataElement) {
            try {
                const rawText = trend30DataElement.textContent || '[]';
                console.log("Raw 30-day data text:", rawText);
                trend30DataRaw = JSON.parse(rawText);
                if (!Array.isArray(trend30DataRaw)) {
                    console.warn("Parsed 30 Day Data is NOT an array, converting to array");
                    trend30DataRaw = [];
                }
                console.log("Successfully parsed 30-day data:", trend30DataRaw.length, "items");
            } catch (e) {
                console.error("Error parsing 30 Day JSON:", e);
                trend30DataRaw = [];
            }
        } else {
            console.warn("Element '#trend-30-data' not found! Charts will show empty state.");
            trend30DataRaw = [];
        }

        if (trend12DataElement) {
            try {
                const rawText = trend12DataElement.textContent || '[]';
                console.log("Raw 12-month data text:", rawText);
                trend12DataRaw = JSON.parse(rawText);
                if (!Array.isArray(trend12DataRaw)) {
                    console.warn("Parsed 12 Month Data is NOT an array, converting to array");
                    trend12DataRaw = [];
                }
                console.log("Successfully parsed 12-month data:", trend12DataRaw.length, "items");
            } catch (e) {
                console.error("Error parsing 12 Month JSON:", e);
                trend12DataRaw = [];
            }
        } else {
            console.warn("Element '#trend-12-data' not found! Charts will show empty state.");
            trend12DataRaw = [];
        }


        if (trendCtx) {
            try {
                // Format data explicitly
                // Format 30-day data
                formatted30DayData = trend30DataRaw.map(item => {
                    const parsedDate = Date.parse(item.date);
                    console.log(`Parsing 30-day date: ${item.date} -> ${parsedDate}`);
                    return { x: parsedDate, y: item.count };
                }).filter(item => {
                    const isValid = item && typeof item.x === 'number' && !isNaN(item.x);
                    if (!isValid) console.warn(`Invalid 30-day data point: ${JSON.stringify(item)}`);
                    return isValid;
                });

                // Format 12-month data
                formatted12MonthData = trend12DataRaw.map(item => {
                    // Ensure we have a valid date string
                    const dateStr = item.month || '';
                    console.log('Raw 12-month item:', item);

                    // Try to parse the date
                    let parsedDate;
                    try {
                        parsedDate = Date.parse(dateStr);
                        console.log(`Parsing 12-month date: ${dateStr} -> ${parsedDate}`);

                        // If parsing failed, try to create a date from year and month
                        if (isNaN(parsedDate) && item.month) {
                            // Try to extract year and month if it's in a different format
                            const parts = item.month.split('-');
                            if (parts.length >= 2) {
                                const year = parseInt(parts[0]);
                                const month = parseInt(parts[1]) - 1; // JS months are 0-based
                                const newDate = new Date(year, month, 1);
                                parsedDate = newDate.getTime();
                                console.log(`Created date from parts: ${year}-${month+1} -> ${parsedDate}`);
                            }
                        }
                    } catch (err) {
                        console.error(`Error parsing date ${dateStr}:`, err);
                        parsedDate = NaN;
                    }

                    return {
                        x: parsedDate,
                        y: item.count || 0 // Ensure count is a number
                    };
                }).filter(item => {
                    const isValid = item && typeof item.x === 'number' && !isNaN(item.x);
                    if (!isValid) console.warn(`Invalid 12-month data point: ${JSON.stringify(item)}`);
                    return isValid;
                });

                // Sort the data by date
                formatted12MonthData.sort((a, b) => a.x - b.x);

                console.log("Formatted 30 Day Data Points:", formatted30DayData.length);
                console.log("Formatted 12 Month Data Points:", formatted12MonthData.length);

                if (formatted30DayData.length > 0 || formatted12MonthData.length > 0) {
                    const initialData = formatted30DayData.length > 0 ? formatted30DayData : formatted12MonthData;
                    const initialUnit = formatted30DayData.length > 0 ? 'day' : 'month';

                    console.log(`Initializing Trend chart with ${initialUnit} data.`);

                    // **** ENHANCED Trend Chart Config ****
                    const trendConfig = {
                        type: 'line',
                        data: {
                            datasets: [{
                                label: 'New Cases',
                                data: initialData,
                                borderColor: currentChartColors.primary,
                                backgroundColor: (context) => {
                                    const chart = context.chart;
                                    const {ctx, chartArea} = chart;
                                    if (!chartArea) return null;

                                    // Parse the primary color to get RGB values
                                    let primaryColor = currentChartColors.primary;
                                    let r, g, b;

                                    // Handle different color formats
                                    if (primaryColor.startsWith('#')) {
                                        // Handle hex format
                                        const hex = primaryColor.substring(1);
                                        r = parseInt(hex.substring(0, 2), 16);
                                        g = parseInt(hex.substring(2, 4), 16);
                                        b = parseInt(hex.substring(4, 6), 16);
                                    } else if (primaryColor.startsWith('rgb')) {
                                        // Handle rgb/rgba format
                                        const rgbMatch = primaryColor.match(/\d+/g);
                                        if (rgbMatch && rgbMatch.length >= 3) {
                                            r = parseInt(rgbMatch[0]);
                                            g = parseInt(rgbMatch[1]);
                                            b = parseInt(rgbMatch[2]);
                                        }
                                    } else if (primaryColor.startsWith('hsl')) {
                                        // For HSL, we'll use a default fallback color
                                        r = 66; g = 133; b = 244; // Default blue
                                    } else {
                                        // Fallback
                                        r = 66; g = 133; b = 244; // Default blue
                                    }

                                    // Create gradient with proper rgba values
                                    const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                                    gradient.addColorStop(0, `rgba(${r}, ${g}, ${b}, 0.1)`);
                                    gradient.addColorStop(0.5, `rgba(${r}, ${g}, ${b}, 0.4)`);
                                    gradient.addColorStop(1, `rgba(${r}, ${g}, ${b}, 0.6)`);
                                    return gradient;
                                },
                                borderWidth: 3,
                                pointRadius: 0,
                                pointHitRadius: 20,
                                pointHoverRadius: 6,
                                pointBackgroundColor: currentChartColors.cardBg,
                                pointBorderColor: currentChartColors.primary,
                                pointBorderWidth: 3,
                                pointHoverBackgroundColor: currentChartColors.cardBg,
                                pointHoverBorderColor: currentChartColors.primary,
                                pointHoverBorderWidth: 4,
                                tension: 0.4,
                                fill: true,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            animation: {
                                duration: 1500,
                                easing: 'easeOutQuart'
                            },
                            scales: {
                                x: {
                                    type: 'time',
                                    adapters: {
                                        date: {}
                                    },
                                    time: {
                                        unit: initialUnit,
                                        tooltipFormat: initialUnit === 'day' ? 'MMM d, yyyy' : 'MMM yyyy',
                                        displayFormats: {
                                            day: 'd MMM',
                                            month: 'MMM yy'
                                        }
                                    },
                                    grid: {
                                        display: false,
                                        drawBorder: false
                                    },
                                    ticks: {
                                        color: currentChartColors.ticksColor,
                                        major: { enabled: true },
                                        maxRotation: 0,
                                        autoSkip: true,
                                        autoSkipPadding: 30,
                                        font: {
                                            size: 11,
                                            weight: 500
                                        }
                                    },
                                    border: {
                                        display: false
                                    }
                                },
                                y: {
                                    beginAtZero: true,
                                    grid: {
                                        color: currentChartColors.gridColor,
                                        drawBorder: false,
                                        lineWidth: 0.5,
                                        borderDash: [3, 3]
                                    },
                                    ticks: {
                                        precision: 0,
                                        color: currentChartColors.ticksColor,
                                        font: {
                                            size: 11,
                                            weight: 500
                                        },
                                        padding: 10,
                                        maxTicksLimit: 6,
                                        callback: function(value) {
                                            if (Number.isInteger(value)) { return value.toLocaleString(); }
                                        }
                                    },
                                    border: {
                                        display: false
                                    }
                                }
                            },
                            plugins: {
                                legend: { display: false },
                                tooltip: {
                                    enabled: true,
                                    intersect: false,
                                    mode: 'index',
                                    position: 'nearest',
                                    backgroundColor: currentChartColors.tooltipBg,
                                    titleFont: {
                                        size: 14,
                                        weight: 'bold'
                                    },
                                    bodyFont: {
                                        size: 13
                                    },
                                    padding: 12,
                                    cornerRadius: 8,
                                    caretSize: 6,
                                    boxPadding: 6,
                                    callbacks: {
                                        title: function(tooltipItems) {
                                            if (tooltipItems.length > 0) {
                                                const date = new Date(tooltipItems[0].parsed.x);
                                                const unit = charts.trend?.options?.scales?.x?.time?.unit || 'day';
                                                const options = unit === 'month'
                                                    ? { year: 'numeric', month: 'long' }
                                                    : { year: 'numeric', month: 'long', day: 'numeric' };
                                                return !isNaN(date) ? date.toLocaleDateString(undefined, options) : 'Invalid Date';
                                            } return '';
                                        },
                                        label: function(context) {
                                            return ` Cases: ${context.parsed.y !== undefined ? context.parsed.y.toLocaleString() : 'N/A'}`;
                                        }
                                    }
                                }
                            },
                            interaction: {
                                mode: 'nearest',
                                axis: 'x',
                                intersect: false
                            }
                        }
                    };
                    // **** End of Refined Config ****

                    charts.trend = new Chart(trendCtx, trendConfig);
                    console.log("Trend chart instance created:", charts.trend);

                    // --- Trend Chart Toggle ---
                    const trend30Btn = document.getElementById('trend30DaysBtn');
                    const trend12Btn = document.getElementById('trend12MonthsBtn');

                    function updateTrendChart(data, unit) {
                        console.log(`Updating trend chart to ${unit} view with data points:`, data.length);
                        if (charts.trend) {
                            try {
                                if (data && data.length > 0) {
                                    // Log data for debugging
                                    console.log(`First data point: ${JSON.stringify(data[0])}`);
                                    console.log(`Last data point: ${JSON.stringify(data[data.length-1])}`);

                                    // Update chart data
                                    charts.trend.data.datasets[0].data = data;

                                    // Update time unit and formats
                                    charts.trend.options.scales.x.time.unit = unit;
                                    charts.trend.options.scales.x.time.tooltipFormat = unit === 'day' ? 'MMM d, yyyy' : 'MMM yyyy';
                                    charts.trend.options.scales.x.time.displayFormats = {
                                        day: 'd MMM',
                                        month: 'MMM yy'
                                    };

                                    // Force complete redraw
                                    charts.trend.update('reset');
                                    console.log("Trend chart updated successfully.");
                                } else {
                                    charts.trend.data.datasets[0].data = [];
                                    charts.trend.options.scales.x.time.unit = unit;
                                    charts.trend.options.scales.x.time.tooltipFormat = unit === 'day' ? 'MMM d, yyyy' : 'MMM yyyy';
                                    charts.trend.update('reset');
                                    console.warn(`No data available for ${unit} view. Chart cleared.`);
                                }
                            } catch (err) {
                                console.error(`Error updating trend chart to ${unit} view:`, err);
                            }
                        } else {
                            console.error("Trend chart instance not found for update.");
                        }
                    }

                    trend30Btn?.addEventListener('click', () => {
                        console.log('30-day button clicked');
                        console.log('30-day data:', formatted30DayData);
                        if (formatted30DayData && formatted30DayData.length > 0) {
                            updateTrendChart(formatted30DayData, 'day');
                            trend30Btn.classList.add('active');
                            trend30Btn.setAttribute('aria-pressed', 'true');
                            trend12Btn?.classList.remove('active');
                            trend12Btn?.setAttribute('aria-pressed', 'false');
                        } else {
                            console.log("No 30-day data to switch to.");
                        }
                    });

                    trend12Btn?.addEventListener('click', () => {
                        console.log('12-month button clicked');
                        console.log('12-month data:', formatted12MonthData);
                        if (formatted12MonthData && formatted12MonthData.length > 0) {
                            updateTrendChart(formatted12MonthData, 'month');
                            trend12Btn.classList.add('active');
                            trend12Btn.setAttribute('aria-pressed', 'true');
                            trend30Btn?.classList.remove('active');
                            trend30Btn?.setAttribute('aria-pressed', 'false');
                        } else {
                            console.log("No 12-month data to switch to.");
                        }
                    });

                    // Set initial active button state
                    if (initialUnit === 'day' && formatted30DayData.length > 0) { trend30Btn?.classList.add('active'); trend30Btn?.setAttribute('aria-pressed', 'true'); }
                    else if (initialUnit === 'month' && formatted12MonthData.length > 0) { trend12Btn?.classList.add('active'); trend12Btn?.setAttribute('aria-pressed', 'true'); }
                    else { trend30Btn?.classList.add('active'); trend30Btn?.setAttribute('aria-pressed', 'true'); } // Default

                } else {
                    console.warn("Trend chart not created: No valid data points found after formatting.");
                    if (trendCanvasElement?.parentNode) { trendCanvasElement.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-graph-down"></i><p class="empty-state-text">No trend data available</p></div>'; }
                }
            } catch (e) {
                console.error("Error processing or creating trend chart:", e);
                if (trendCanvasElement?.parentNode) { trendCanvasElement.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-exclamation-triangle"></i><p class="empty-state-text">Error loading trend data</p></div>'; }
            }
        } else {
            console.warn("Trend chart canvas element ('#trendChart') not found or context could not be obtained.");
            if(trendCanvasElement?.parentNode) { trendCanvasElement.parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-graph-down"></i><p class="empty-state-text">Chart canvas not found</p></div>'; }
            else if (document.getElementById('trendChart')) { console.error("Canvas element found, but failed to get 2D context."); document.getElementById('trendChart').parentNode.innerHTML = '<div class="empty-state text-center"><i class="bi bi-exclamation-triangle"></i><p class="empty-state-text">Failed to initialize chart context</p></div>'; }
        }


        // --- Date range filter & Search ---
        document.getElementById('dateRangeFilter')?.addEventListener('change', function() { const days = this.value; const currentUrl = new URL(window.location.href); currentUrl.searchParams.set('range', days); window.location.href = currentUrl.toString(); });
        window.performSearch = function() { const searchTerm = document.getElementById('globalSearch')?.value.trim(); if (searchTerm) { const searchUrl = "{% url 'case:search_cases' %}"; if(searchUrl) { window.location.href = `${searchUrl}?q=${encodeURIComponent(searchTerm)}`; } else { console.error("Search URL 'case:search_cases' not found."); } } }
        document.getElementById('globalSearch')?.addEventListener('keypress', function(e) { if (e.key === 'Enter') { e.preventDefault(); performSearch(); } });

        // --- Initial Chart Defaults and Options Update ---
        updateChartDefaults();
        updateExistingChartOptions();
        console.log("Dashboard JS Initialization Complete.");

    }); // End DOMContentLoaded
    </script>
{% endblock %}